import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// API Environment mapping
const API_ENVIRONMENTS: Record<string, string> = {
  development: 'https://b2b.mb.mb-gaming.life',
  production: 'https://dash.api.dev.mossbets.bet'
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '')

  // Determine target API URL
  const apiEnvironment = env.VITE_API_ENVIRONMENT || 'development'
  const targetUrl = env.VITE_API_BASE_URL || API_ENVIRONMENTS[apiEnvironment] || API_ENVIRONMENTS.development

  console.log(`Vite Config - Mode: ${mode}`)
  console.log(`API Environment: ${apiEnvironment}`)
  console.log(`Proxy Target: ${targetUrl}`)

  return {
    plugins: [
      vue(),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      proxy: {
        // Proxy API requests to avoid CORS issues in development
        '/api': {
          target: targetUrl,
          changeOrigin: true,
          secure: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('❌ Proxy error:', err);
            });
            proxy.on('proxyReq', (_proxyReq, req, _res) => {
              console.log(`🚀 Proxying: ${req.method} ${req.url} -> ${targetUrl}`);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log(`✅ Response: ${proxyRes.statusCode} ${req.url}`);
            });
          },
        }
      }
    }
  }
})
