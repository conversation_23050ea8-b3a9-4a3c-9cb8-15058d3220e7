# API Environment Configuration

This document explains how to configure and switch between different API environments in the Mossbets B2B BO application.

## Available Environments

### Development
- **URL**: `https://b2b.mb.mb-gaming.life`
- **Description**: Development environment for testing
- **Default**: Yes (when `VITE_API_ENVIRONMENT=development`)

### Production
- **URL**: `https://dash.api.dev.mossbets.bet`
- **Description**: Production environment
- **Default**: When `VITE_API_ENVIRONMENT=production`

## Environment Configuration Files

### `.env` (Default/Committed)
Contains default configuration values that are committed to version control:
```env
VITE_API_ENVIRONMENT=development
VITE_API_BASE_URL=https://b2b.mb.mb-gaming.life
VITE_API_TIMEOUT=30000
VITE_DEBUG_API=false
```

### `.env.local` (Local Overrides)
For local development overrides. **NOT committed to version control**:
```env
# Uncomment to switch to production API locally
# VITE_API_ENVIRONMENT=production
# VITE_API_BASE_URL=https://dash.api.dev.mossbets.bet

# Enable debugging
# VITE_DEBUG_API=true
```

### `.env.development` / `.env.production`
Environment-specific configurations loaded automatically based on build mode.

## How to Switch Environments

### Method 1: Environment Variables
1. **For local development**: Edit `.env.local` file
2. **For build**: Set environment variables or use appropriate `.env.*` file

```bash
# Switch to production API locally
echo "VITE_API_ENVIRONMENT=production" >> .env.local
echo "VITE_API_BASE_URL=https://dash.api.dev.mossbets.bet" >> .env.local

# Or switch back to development
echo "VITE_API_ENVIRONMENT=development" >> .env.local
echo "VITE_API_BASE_URL=https://b2b.mb.mb-gaming.life" >> .env.local
```

### Method 2: Manual Switching (Development Only)
In development mode, use the API Environment Switcher component:

1. Look for the floating panel in the bottom-right corner
2. Click to expand and see available environments
3. Click on an environment to switch
4. Reload the page when prompted

### Method 3: Build-time Configuration
```bash
# Build for production with production API
npm run build

# Build for development with development API
npm run build -- --mode development
```

## Environment Variables Reference

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `VITE_API_ENVIRONMENT` | Current API environment | `development` | `production` |
| `VITE_API_BASE_URL` | Override base URL | Auto-detected | `https://dash.api.dev.mossbets.bet` |
| `VITE_API_TIMEOUT` | Request timeout (ms) | `30000` | `60000` |
| `VITE_DEBUG_API` | Enable API debugging | `false` | `true` |

## Development Proxy

In development mode (`npm run dev`), all API requests are proxied through Vite's dev server to avoid CORS issues:

- **Client requests**: `http://localhost:5173/api/*`
- **Proxied to**: Configured API environment
- **Rewrite**: `/api/users` → `/users`

## Programmatic Access

```typescript
import { 
  getCurrentApiConfig, 
  switchApiEnvironment,
  getAvailableApiEnvironments 
} from '@/config/api'

// Get current configuration
const config = getCurrentApiConfig()
console.log(config.baseUrl, config.environment)

// Switch environment (development only)
switchApiEnvironment('production')

// Get all available environments
const environments = getAvailableApiEnvironments()
```

## Troubleshooting

### Environment not switching
1. Check `.env.local` file for overrides
2. Clear browser localStorage: `localStorage.clear()`
3. Restart development server
4. Check console for configuration logs

### CORS issues
- In development: Ensure proxy is working (check Vite config)
- In production: Verify API server CORS configuration

### API requests failing
1. Check network tab for actual request URLs
2. Verify environment configuration with `getCurrentApiConfig()`
3. Enable debug logging: `VITE_DEBUG_API=true`

## Best Practices

1. **Never commit `.env.local`** - Add to `.gitignore`
2. **Use environment-specific files** for different deployment targets
3. **Test both environments** before deploying
4. **Enable debug logging** during development
5. **Document API changes** that affect environment configuration
