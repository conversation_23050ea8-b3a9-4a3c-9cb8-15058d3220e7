# Vue.js Dashboard Application Recreation Guide

This comprehensive guide will help you recreate a professional Vue.js dashboard application with all the common features and structure patterns used in this project.

## Table of Contents
1. [Project Setup](#project-setup)
2. [Core Architecture](#core-architecture)
3. [Authentication System](#authentication-system)
4. [Layout Components](#layout-components)
5. [Data Management](#data-management)
6. [Permission System](#permission-system)
7. [Utility Functions](#utility-functions)
8. [Common Patterns](#common-patterns)

## Project Setup

### 1. Initialize Vue 3 Project
```bash
npm create vue@latest my-dashboard-app
cd my-dashboard-app
npm install
```

### 2. Essential Dependencies
```bash
# Core dependencies
npm install pinia pinia-plugin-persistedstate
npm install vue-router@4
npm install axios
npm install @heroicons/vue
npm install @headlessui/vue
npm install @vueuse/core

# Development dependencies
npm install -D @types/node
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

### 3. Project Structure
```
src/
├── components/
│   ├── Layout/
│   │   ├── Sidebar.vue
│   │   ├── TopBar.vue
│   │   └── MainLayout.vue
│   ├── Modals/
│   │   ├── PermissionAssignmentModal.vue
│   │   └── PartnerSelectionModal.vue
│   └── DataTable.vue
├── views/
│   ├── Auth/
│   │   ├── Login.vue
│   │   └── VerifyOTP.vue
│   ├── System/
│   │   ├── SystemUsers.vue
│   │   ├── SystemRoles.vue
│   │   └── RolesPermissions.vue
│   ├── Partners/
│   │   └── PartnersList.vue
│   └── Dashboard.vue
├── stores/
│   ├── auth.ts
│   └── sidebar.ts
├── services/
│   ├── apiClient.ts
│   ├── authApi.ts
│   ├── systemApi.ts
│   ├── partnerApi.ts
│   └── types.ts
├── utils/
│   ├── permissions.ts
│   └── formatters.ts
├── plugins/
│   └── permissions.ts
└── router/
    └── index.ts
```

## Core Architecture

### 1. API Client Setup (`src/services/apiClient.ts`)
```typescript
import axios from 'axios'

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor for auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers['X-Access'] = token
  }
  return config
})

export { apiClient }
```

### 2. Pinia Store Setup (`src/stores/auth.ts`)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('auth_token'))
  const selectedPartnerId = ref(null)
  
  const isAuthenticated = computed(() => !!token.value)
  const isSuperUser = computed(() => user.value?.role_name === 'Super Admin')
  
  const login = async (credentials) => {
    // Login logic
  }
  
  const logout = () => {
    user.value = null
    token.value = null
    selectedPartnerId.value = null
    localStorage.removeItem('auth_token')
  }
  
  return {
    user, token, selectedPartnerId,
    isAuthenticated, isSuperUser,
    login, logout
  }
}, {
  persist: {
    paths: ['user', 'token', 'selectedPartnerId']
  }
})
```

## Authentication System

### 1. Hash-based Authentication
```typescript
// src/services/authApi.ts
import CryptoJS from 'crypto-js'

export const createHashKey = (payload: any): string => {
  const secretKey = import.meta.env.VITE_SECRET_KEY
  const jsonString = JSON.stringify(payload)
  return CryptoJS.HmacSHA256(jsonString, secretKey).toString()
}

export const authApi = {
  async login(credentials) {
    const payload = {
      username: credentials.username,
      password: credentials.password,
      timestamp: Math.floor(Date.now() / 1000)
    }
    
    const hash = createHashKey(payload)
    
    return apiClient.post('/auth/login', payload, {
      headers: { 'X-Hash-Key': hash }
    })
  }
}
```

### 2. OTP Verification with Partner Selection
```typescript
// After successful OTP verification
if (user?.partners && user.partners.length > 1) {
  showPartnerSelectionModal.value = true
} else {
  router.push({ name: 'dashboard' })
}
```

## Layout Components

### 1. Responsive Sidebar (`src/components/Sidebar.vue`)
```vue
<template>
  <aside
    class="bg-gradient-to-b from-slate-800 to-slate-900 shadow-xl sidebar-transition flex flex-col overflow-hidden"
    :class="[
      sidebarStore.sidebarWidth,
      {
        'fixed inset-y-0 left-0 z-50': sidebarStore.isMobile,
        'fixed inset-y-0 left-0 z-20': !sidebarStore.isMobile
      }
    ]"
  >
    <!-- Sidebar content with exclusive dropdown menus -->
  </aside>
</template>
```

### 2. TopBar with User Info (`src/components/TopBar.vue`)
```vue
<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
    <div class="flex items-center justify-between px-4 py-3">
      <!-- Mobile menu button -->
      <button @click="sidebarStore.toggle()" class="md:hidden">
        <Bars3Icon class="w-6 h-6" />
      </button>
      
      <!-- User info and actions -->
      <div class="flex items-center space-x-4">
        <span>{{ authStore.user?.role_name }}</span>
        <UserMenu />
      </div>
    </div>
  </header>
</template>
```

## Data Management

### 1. Standard API Structure
All API endpoints should include these standard fields:
```typescript
interface StandardApiParams {
  timestamp: number
  start?: string
  end?: string
  status?: string | number
  export?: boolean
  limit?: number
  page?: number
}
```

### 2. DataTable Component
```vue
<template>
  <DataTable
    :data="items"
    :headers="tableHeaders"
    :loading="loading"
    :exclude-columns="['password', 'internal_id']"
    :auto-format="true"
    @page-change="handlePageChange"
  >
    <template #cell-status="{ item }">
      <StatusBadge :status="item.status" />
    </template>
  </DataTable>
</template>
```

### 3. Auto-formatting Utilities
```typescript
// src/utils/formatters.ts
export const formatCurrency = (amount: number, currency = 'KES', decimals = 2) => {
  const formatted = amount.toLocaleString('en-KE', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
  return `${currency} ${formatted}`
}

export const formatDate = (date: string | Date, format = 'short') => {
  const dateObj = new Date(date)
  const options = { timeZone: 'Africa/Nairobi' }
  
  switch (format) {
    case 'short': // dd/mm/yyyy
      return dateObj.toLocaleDateString('en-GB', options)
    case 'datetime': // dd/mm/yyyy hh:mm
      return dateObj.toLocaleString('en-GB', { 
        ...options, 
        hour12: false 
      })
    default:
      return dateObj.toLocaleDateString('en-GB', options)
  }
}
```

## Permission System

### 1. Permission Utilities (`src/utils/permissions.ts`)
```typescript
export const hasPermission = (permissionId: string | number): boolean => {
  const authStore = useAuthStore()
  return authStore.user?.permissions?.some(p => 
    p.id.toString() === permissionId.toString()
  ) || false
}

export const hasRole = (roleId: string | number): boolean => {
  const authStore = useAuthStore()
  return authStore.user?.role_id?.toString() === roleId.toString()
}

export const canPerformOperation = (resource: string, operation: string): boolean => {
  const patterns = [
    `${resource}.${operation}`,
    `${operation}_${resource}`,
    `manage_${resource}`
  ]
  return patterns.some(pattern => hasPermissionByName(pattern))
}
```

### 2. Vue Plugin for Global Access
```typescript
// src/plugins/permissions.ts
export default {
  install(app: App) {
    app.config.globalProperties.$can = hasPermissionByName
    app.config.globalProperties.$hasRole = hasRoleByName
    app.config.globalProperties.$isSuperUser = isSuperUser
  }
}
```

### 3. Usage in Templates
```vue
<template>
  <button v-if="$can('create_users')">Add User</button>
  <div v-if="$hasRole('admin')">Admin Panel</div>
  <router-link v-if="$can('manage_partners')" to="/partners">
    Partners
  </router-link>
</template>
```

## Common Patterns

### 1. Modal Management
```typescript
const showModal = ref(false)
const selectedItem = ref(null)

const editItem = (item) => {
  selectedItem.value = item
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedItem.value = null
}
```

### 2. Filter Management
```typescript
const filters = reactive({
  search: '',
  status: '',
  start: '',
  end: ''
})

// Auto-refresh on filter changes
watch(filters, () => {
  pagination.current_page = 1
  loadData()
}, { deep: true })
```

### 3. Pagination Handling
```typescript
const pagination = reactive({
  current_page: 1,
  limit: 10,
  total: 0
})

const handlePageChange = (page: number) => {
  pagination.current_page = page
  loadData()
}
```

### 4. Error Handling
```typescript
const handleApiError = (error: any) => {
  if (error.response?.status === 401) {
    authStore.logout()
    router.push('/login')
  } else {
    // Show error message
    console.error('API Error:', error)
  }
}
```

## Best Practices

1. **Consistent API Structure**: All endpoints use the same parameter structure
2. **Responsive Design**: Mobile-first approach with Tailwind CSS
3. **Type Safety**: TypeScript interfaces for all data structures
4. **State Management**: Pinia stores with persistence
5. **Permission-based UI**: Show/hide elements based on user permissions
6. **Auto-formatting**: Consistent data display across the application
7. **Error Handling**: Graceful error handling with user feedback
8. **Loading States**: Show loading indicators during API calls

This guide provides the foundation for creating a professional, scalable Vue.js dashboard application with all the modern features and patterns used in enterprise applications.
