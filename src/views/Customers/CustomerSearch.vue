<template>
  <div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Customer Search</h1>
        <p class="mt-1 text-sm text-gray-500">
          Search for customers by phone number and view their details
        </p>
      </div>
    </div>

    <!-- Search Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
            Phone Number
          </label>
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              id="phone"
              v-model="phone"
              type="tel"
              placeholder="Enter phone number to search (e.g., 0714919776)"
              class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              @keyup.enter="searchPhone"
            />
          </div>
        </div>
        <div class="flex-shrink-0">
          <button
            @click="searchPhone"
            :disabled="loading || !phone.trim()"
            class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <MagnifyingGlassIcon v-if="!loading" class="h-4 w-4 mr-2" />
            <div v-else class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {{ loading ? 'Searching...' : 'Search' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <XMarkIcon class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error</h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ error }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Details -->
    <div v-else-if="customer" class="space-y-6">
      <!-- Customer Profile Cards -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Profile Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div class="text-center">
            <div class="mx-auto h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center mb-4">
              <UserIcon class="h-10 w-10 text-gray-400" />
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ customer.name }}</h3>
            <div class="space-y-1 text-gray-600">
              <p class="text-lg">+{{ customer.msisdn }}</p>
              <p>{{ customer.loan_number }}</p>
              <p>{{ customer.employee_number }}</p>
              <p class="font-medium text-blue-600">{{ customer.client_name }}</p>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="mt-6 flex justify-center space-x-3">
            <ActionButton
              :variant="customer.marketing_status === '1' ? 'warning' : 'success'"
              size="sm"
              shape="rounded"
              @click="toggleBulkStatus"
            >
              {{ customer.marketing_status === '1' ? 'Disable Bulk' : 'Enable Bulk' }}
            </ActionButton>
            <ActionButton
              variant="danger"
              size="sm"
              shape="rounded"
              @click="toggleBlock"
            >
              Block
            </ActionButton>
          </div>
        </div>

        <!-- Details Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Details</h3>

          <div class="space-y-4">
            <div class="flex justify-between">
              <span class="font-medium text-gray-700">Max Approved Loan</span>
              <span class="text-gray-900 font-semibold">{{ formatCurrency(customer.max_approved) }}</span>
            </div>

            <div class="flex justify-between">
              <span class="font-medium text-gray-700">Credit Score</span>
              <span class="text-gray-900 font-semibold">{{ customer.credit_score }}</span>
            </div>

            <div class="flex justify-between">
              <span class="font-medium text-gray-700">Employment Date</span>
              <span class="text-gray-900">{{ formatDateTime(customer.employment_date) }}</span>
            </div>

            <div class="flex justify-between">
              <span class="font-medium text-gray-700">Last Login</span>
              <span class="text-gray-900">{{ customer.last_login_date ? formatDateTime(customer.last_login_date) : 'Never' }}</span>
            </div>
          </div>

          <div class="mt-6 pt-4 border-t border-gray-200">
            <h4 class="font-medium text-gray-900 mb-3 text-center">Account Balances</h4>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="font-medium text-gray-700">Actual Balance</span>
                <span class="text-gray-900 font-semibold">{{ formatCurrency(customer.actual_balance) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium text-gray-700">Loan Balance</span>
                <span class="text-gray-900 font-semibold">{{ formatCurrency(customer.loan_balance) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs Section -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="switchTab(tab.id)"
              :class="[
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'
              ]"
            >
              <component :is="tab.icon" class="h-5 w-5 mr-2" />
              {{ tab.name }}
              <span v-if="tab.count !== undefined" class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
                {{ tab.count }}
              </span>
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
          <!-- Loan Requests Tab -->
          <div v-if="activeTab === 'loan_requests'">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900">Loan Requests</h3>
            </div>
            <div v-if="tabLoading" class="flex justify-center py-8">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
            <div v-else-if="loanRequests.length === 0" class="text-center py-8 text-gray-500">
              <DocumentTextIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No loan requests found for this customer.</p>
            </div>
            <div v-else class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request No</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="request in loanRequests" :key="request.req_number">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ request.req_number }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request.product_name || 'N/A' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(request.requested_amount) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(request.approved_amount) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusClass(request.approval_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ getStatusText(request.approval_status) }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDateTime(request.created) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Other tabs content placeholder -->
          <div v-if="activeTab !== 'loan_requests'" class="text-center py-12 text-gray-500">
            <component :is="getCurrentTabIcon()" class="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>{{ activeTab.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) }} data will be displayed here.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- No Results State -->
    <div v-else-if="searchPerformed && !customer" class="text-center py-12">
      <UserIcon class="mx-auto h-12 w-12 text-gray-400 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No customer found</h3>
      <p class="text-gray-500">Try searching with a different phone number.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  UserIcon,
  BanknotesIcon,
  DocumentTextIcon,
  ScaleIcon,
  CreditCardIcon,
  DocumentCheckIcon
} from '@heroicons/vue/24/outline'
import ActionButton from '@/components/ActionButton.vue'
import { customerApi } from '@/services/customerApi'

// Reactive data
const loading = ref(false)
const tabLoading = ref(false)
const error = ref('')
const phone = ref('')
const customer = ref<any>(null)
const searchPerformed = ref(false)
const activeTab = ref('loan_requests')

// Tab data
const loanRequests = ref([])

// Tab configuration
const tabs = [
  { id: 'loan_requests', name: 'Loan Requests', icon: DocumentTextIcon, count: 0 },
  { id: 'repayments', name: 'Repayments', icon: CreditCardIcon, count: 0 },
  { id: 'limits', name: 'Limits', icon: ScaleIcon, count: 0 },
  { id: 'transactions', name: 'Transactions', icon: BanknotesIcon, count: 0 },
  { id: 'kyc', name: 'KYC', icon: DocumentCheckIcon, count: 0 }
]

// Methods
const searchPhone = async () => {
  if (!phone.value.trim()) return

  loading.value = true
  error.value = ''
  searchPerformed.value = true

  try {
    // Format phone number
    let formattedPhone = phone.value.trim()
    if (formattedPhone.startsWith('0')) {
      formattedPhone = formattedPhone.substring(1)
    }

    const response = await customerApi.searchCustomer({ loan_number: formattedPhone })

    if (response.status === 200 && response.message?.data?.length > 0) {
      customer.value = response.message.data[0]
      // Automatically load loan requests
      await fetchLoanRequests()
    } else {
      customer.value = null
      error.value = 'No customer found with this phone number'
    }
  } catch (err: any) {
    error.value = err.message || 'An error occurred while searching'
    customer.value = null
  } finally {
    loading.value = false
  }
}

const switchTab = async (tabId: string) => {
  activeTab.value = tabId

  switch (tabId) {
    case 'loan_requests':
      await fetchLoanRequests()
      break
    // Other tabs will be implemented later
  }
}

const fetchLoanRequests = async () => {
  if (!customer.value) return

  tabLoading.value = true
  try {
    const response = await customerApi.getLoanRequests({
      loan_number: customer.value.loan_number,
      limit: 10,
      offset: 1
    })

    if (response.status === 200) {
      loanRequests.value = response.message?.data || []
    }
  } catch (err) {
    console.error('Error fetching loan requests:', err)
  } finally {
    tabLoading.value = false
  }
}

const toggleBulkStatus = async () => {
  console.log('Toggle bulk status for:', customer.value?.name)
}

const toggleBlock = async () => {
  console.log('Toggle block for:', customer.value?.name)
}

const getCurrentTabIcon = () => {
  const tab = tabs.find(t => t.id === activeTab.value)
  return tab?.icon || DocumentTextIcon
}

const getStatusClass = (status: string) => {
  const statusNum = parseInt(status)
  switch (statusNum) {
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-orange-100 text-orange-800'
    case 3: return 'bg-red-100 text-red-800'
    case 4: return 'bg-purple-100 text-purple-800'
    case 5: return 'bg-blue-100 text-blue-800'
    case 6: return 'bg-yellow-100 text-yellow-800'
    case 7: return 'bg-red-100 text-red-800'
    case 8: return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  const statusNum = parseInt(status)
  switch (statusNum) {
    case 1: return 'Fully Paid'
    case 2: return 'Partially Paid'
    case 3: return 'Rejected'
    case 4: return 'Unverified'
    case 5: return 'Pending'
    case 6: return 'Unpaid'
    case 7: return 'Failure'
    case 8: return 'Approved'
    default: return 'Unknown'
  }
}

// Utility functions
const formatCurrency = (amount: string | number) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES'
  }).format(numAmount || 0)
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-KE', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
