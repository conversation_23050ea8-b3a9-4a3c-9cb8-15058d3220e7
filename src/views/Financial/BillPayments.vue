<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Bill Payments</h1>
          <p class="text-gray-600 mt-1">Manage bill payment transactions and utilities</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
          <router-link
            :to="{ name: 'add-bill-payment' }"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <PlusIcon class="w-4 h-4 mr-2" />
            Add Utility
          </router-link>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Utility</label>
          <select
            v-model="filters.utility_id"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Utilities</option>
            <option v-for="utility in utilities" :key="utility.value" :value="utility.value">
              {{ utility.text }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="filters.status"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Status</option>
            <option value="1">Active</option>
            <option value="0">Inactive</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
          <input
            v-model="filters.start"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
          <input
            v-model="filters.end"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      <div class="mt-4 flex justify-end space-x-3">
        <button
          @click="clearFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Clear
        </button>
        <button
          @click="applyFilters"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Apply Filters
        </button>
      </div>
    </div>

    <!-- Bill Payment Transactions Table -->
    <DataTable
      :data="billPaymentTransactions"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Bill Payment Transactions"
      row-key="id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-reference_number="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.reference_number }}
        </div>
      </template>

      <template #cell-customer="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">+{{ row.customer ? row.customer.split(' - ')[0] : '' }}</div>
          <div class="text-gray-500">{{ row.customer ? row.customer.split(' - ')[1] : '' }}</div>
        </div>
      </template>

      <template #cell-receipt_info="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.receipt_number }}</div>
          <div class="text-gray-500">{{ row.narration }}</div>
        </div>
      </template>

      <template #cell-amount="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.currency_code }}.{{ formatCurrency(row.amount) }}
        </div>
      </template>

      <template #cell-discount="{ row }">
        <div class="text-sm text-gray-900">
          {{ row.discount || '0' }}
        </div>
      </template>

      <template #cell-created="{ row }">
        <div class="text-sm text-gray-900">
          {{ formatDate(row.created) }}
        </div>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ row, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>

          <div
            v-if="showDropdown[index]"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="py-1">
              <button
                @click="viewDetails(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Details
              </button>
              <button
                @click="downloadReceipt(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"
              >
                Download Receipt
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon,
  EllipsisVerticalIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import { billPaymentsApi, type BillPaymentTransaction } from '@/services/billPaymentsApi'
import { useAuthStore } from '@/stores/auth'

// Router and auth store
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const billPaymentTransactions = ref<BillPaymentTransaction[]>([])
const utilities = ref<Array<{ text: string; value: string }>>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showDropdown = reactive<Record<number, boolean>>({})

// Filters
const filters = ref({
  utility_id: '',
  status: '',
  start: '',
  end: '',
  reference_number: '',
  receipt_number: '',
  client_phone: ''
})

// Table headers
const tableHeaders = [
  { key: 'reference_number', label: 'Ref No', sortable: true },
  { key: 'customer', label: 'Profile', sortable: false },
  { key: 'receipt_info', label: 'Receipt No & Desc', sortable: false },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'discount', label: 'Discount', sortable: true },
  { key: 'created', label: 'Date', sortable: true }
]

// Methods
const fetchBillPaymentTransactions = async () => {
  loading.value = true
  try {
    const response = await billPaymentsApi.getBillPaymentTransactions({
      page: currentPage.value,
      limit: pageSize.value,
      ...filters.value
    })

    if (response.status === 200) {
      billPaymentTransactions.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      billPaymentTransactions.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching bill payment transactions:', error)
    billPaymentTransactions.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const fetchUtilities = async () => {
  try {
    const response = await billPaymentsApi.getBillPayments({ limit: 100 })
    if (response.status === 200) {
      utilities.value = [
        { text: 'All', value: '' },
        ...(response.message?.data || []).map(item => ({
          text: item.bill_name,
          value: item.id
        }))
      ]
    }
  } catch (error) {
    console.error('Error fetching utilities:', error)
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchBillPaymentTransactions()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchBillPaymentTransactions()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchBillPaymentTransactions()
}

const handleRowClick = (row: BillPaymentTransaction) => {
  viewDetails(row)
}

const refreshData = () => {
  fetchBillPaymentTransactions()
  fetchUtilities()
}

const applyFilters = () => {
  currentPage.value = 1
  fetchBillPaymentTransactions()
}

const clearFilters = () => {
  filters.value = {
    utility_id: '',
    status: '',
    start: '',
    end: '',
    reference_number: '',
    receipt_number: '',
    client_phone: ''
  }
  currentPage.value = 1
  fetchBillPaymentTransactions()
}

const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })
  // Toggle current dropdown
  showDropdown[index] = !showDropdown[index]
}

const viewDetails = (transaction: BillPaymentTransaction) => {
  console.log('View details for transaction:', transaction.reference_number)
  // TODO: Navigate to transaction details page or open modal
}

const downloadReceipt = (transaction: BillPaymentTransaction) => {
  console.log('Download receipt for transaction:', transaction.reference_number)
  // TODO: Implement receipt download functionality
}

const exportData = () => {
  console.log('Export bill payment transactions data')
  // TODO: Implement export functionality
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  try {
    const date = new Date(dateString)
    return date.toLocaleString()
  } catch (error) {
    return 'N/A'
  }
}

// Initialize data
onMounted(() => {
  fetchUtilities()
  fetchBillPaymentTransactions()
})
</script>
