<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            Transactions
            <span v-if="selectedClientName" class="text-lg text-gray-600">- ({{ selectedClientName }})</span>
          </h1>
          <p class="text-gray-600 mt-1">View and manage all financial transactions</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Filter By</label>
          <select
            v-model="selectedFilter"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option v-for="filter in filterOptions" :key="filter.value" :value="filter">
              {{ filter.text }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ selectedFilter.text }}</label>
          <div v-if="selectedFilter.value === 1" class="relative">
            <input
              v-model="searchClient"
              @click="toggleSearchDropdown"
              :placeholder="searchDropdownPlaceholder || 'Search organization'"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <div
              v-if="searchDropdown"
              class="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
            >
              <div
                v-for="org in organizations"
                :key="org.value"
                @click="setClientId(org)"
                class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
              >
                {{ org.text }}
              </div>
            </div>
          </div>
          <input
            v-else-if="selectedFilter.value === 2"
            v-model="filters.loan_number"
            type="text"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter loan number"
          />
          <input
            v-else-if="selectedFilter.value === 3"
            v-model="filters.client_email"
            type="email"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter email address"
          />
          <input
            v-else-if="selectedFilter.value === 4"
            v-model="filters.client_phone"
            type="text"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter phone number"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <div class="flex space-x-2">
            <input
              v-model="filters.start"
              type="date"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <input
              v-model="filters.end"
              type="date"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
          <input
            v-model="filters.amount"
            type="number"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter amount"
          />
        </div>
      </div>

      <div class="mt-4 flex justify-end space-x-3">
        <button
          @click="clearFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Clear
        </button>
        <button
          @click="applyFilters"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Apply Filters
        </button>
      </div>
    </div>

    <!-- Transactions Table -->
    <DataTable
      :data="transactions"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Transaction History"
      row-key="transaction_id"
      :has-actions="false"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-transaction_id="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.transaction_id }}
        </div>
      </template>

      <template #cell-customer="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.msisdn }}</div>
          <div class="text-gray-500">{{ row.first_name }} {{ row.last_name }}</div>
        </div>
      </template>

      <template #cell-transaction_type="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.reference_name }}</div>
          <div class="text-gray-500 text-xs">{{ row.source }}</div>
        </div>
      </template>

      <template #cell-amount="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.currency_code }}.{{ formatCurrency(row.amount) }}
        </div>
      </template>

      <template #cell-description="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.identifier_name }} | {{ row.reference_type }} | {{ row.channel_name }}</div>
          <div class="text-gray-500 text-xs">{{ row.description }}</div>
        </div>
      </template>

      <template #cell-created="{ row }">
        <div class="text-sm text-gray-900">
          {{ formatDate(row.created) }}
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import { billPaymentsApi } from '@/services/billPaymentsApi'
import { useAuthStore } from '@/stores/auth'

// Router and auth store
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const transactions = ref<any[]>([])
const organizations = ref<Array<{ text: string; value: string }>>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Search and filter state
const searchClient = ref('')
const searchDropdown = ref(false)
const searchDropdownPlaceholder = ref('')
const selectedClientName = ref('')

// Filter options
const filterOptions = [
  { text: 'Organization Name', value: 1 },
  { text: 'Loan Number', value: 2 },
  { text: 'Email', value: 3 },
  { text: 'Phone Number', value: 4 }
]

const selectedFilter = ref(filterOptions[0])

// Filters
const filters = ref({
  client_id: authStore.selectedClientId || '',
  loan_number: route.query.loan_number as string || '',
  client_phone: '',
  client_email: '',
  start: '',
  end: '',
  amount: '',
  source: '',
  channel_name: '',
  reference_type_id: '',
  trxn_reference_id: ''
})

// Table headers
const tableHeaders = [
  { key: 'transaction_id', label: 'Trxn ID', sortable: true },
  { key: 'customer', label: 'Customer', sortable: false },
  { key: 'transaction_type', label: 'Trxn Type', sortable: false },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'description', label: 'Description', sortable: false },
  { key: 'created', label: 'Date', sortable: true }
]

// Methods
const fetchTransactions = async () => {
  loading.value = true
  try {
    const response = await billPaymentsApi.getTransactions({
      page: currentPage.value,
      limit: pageSize.value,
      ...filters.value
    })

    if (response.status === 200) {
      transactions.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      transactions.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching transactions:', error)
    transactions.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const fetchOrganizations = async () => {
  // Organizations functionality removed as per requirements
  organizations.value = []
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchTransactions()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchTransactions()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchTransactions()
}

const handleRowClick = (row: any) => {
  console.log('Transaction details:', row)
  // TODO: Navigate to transaction details page or open modal
}

const refreshData = () => {
  fetchTransactions()
  fetchOrganizations()
}

const applyFilters = () => {
  currentPage.value = 1
  fetchTransactions()
}

const clearFilters = () => {
  filters.value = {
    client_id: authStore.selectedClientId || '',
    loan_number: '',
    client_phone: '',
    client_email: '',
    start: '',
    end: '',
    amount: '',
    source: '',
    channel_name: '',
    reference_type_id: '',
    trxn_reference_id: ''
  }
  searchDropdownPlaceholder.value = ''
  selectedClientName.value = ''
  currentPage.value = 1
  fetchTransactions()
}

const toggleSearchDropdown = () => {
  searchDropdown.value = !searchDropdown.value
}

const setClientId = (org: { text: string; value: string }) => {
  filters.value.client_id = org.value
  searchDropdownPlaceholder.value = org.text
  selectedClientName.value = org.text
  searchDropdown.value = false
  searchClient.value = ''
  fetchTransactions()
}

const exportData = () => {
  console.log('Export transactions data')
  // TODO: Implement export functionality
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  try {
    const date = new Date(dateString)
    return date.toLocaleString()
  } catch (error) {
    return 'N/A'
  }
}

// Watch for route changes
watch(() => route.params.client_id, (newClientId) => {
  if (newClientId) {
    filters.value.client_id = newClientId as string
    const org = organizations.value.find(org => org.value === newClientId)
    if (org) {
      searchDropdownPlaceholder.value = org.text
      selectedClientName.value = org.text
    }
    fetchTransactions()
  }
})

watch(() => route.query.loan_number, (newLoanNumber) => {
  if (newLoanNumber) {
    filters.value.loan_number = newLoanNumber as string
    fetchTransactions()
  }
})

// Initialize data
onMounted(() => {
  fetchOrganizations()
  fetchTransactions()
})
</script>
