<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Withdrawals</h1>
          <p class="text-gray-600 mt-1">Process and manage withdrawal requests</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
          <input
            v-model="filters.reference_number"
            type="text"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Enter reference number"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="filters.withdrawal_status"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Status</option>
            <option value="1">Successful</option>
            <option value="0">Failed</option>
            <option value="2">Pending</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
          <input
            v-model="filters.start"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
          <input
            v-model="filters.end"
            type="date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>

      <div class="mt-4 flex justify-end space-x-3">
        <button
          @click="clearFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Clear
        </button>
        <button
          @click="applyFilters"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Apply Filters
        </button>
      </div>
    </div>

    <!-- Withdrawals Table -->
    <DataTable
      :data="withdrawals"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Withdrawal Transactions"
      row-key="reference_number"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-reference_number="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.reference_number }}
        </div>
      </template>

      <template #cell-receipt_number="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.receipt_number }}
        </div>
      </template>

      <template #cell-customer="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">+{{ row.customer ? row.customer.split('-')[0] : '' }}</div>
          <div class="text-gray-500">{{ row.customer ? row.customer.split('-')[1] : '' }}</div>
        </div>
      </template>

      <template #cell-description="{ row }">
        <div class="text-sm text-gray-900">
          {{ row.response_desc }}
        </div>
      </template>

      <template #cell-amount="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.currency_code }}.{{ formatCurrency(row.amount) }}
        </div>
      </template>

      <template #cell-charges="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.currency_code }}.{{ formatCurrency(row.charges) }}
        </div>
      </template>

      <template #cell-created="{ row }">
        <div class="text-sm text-gray-900">
          {{ formatDate(row.created) }}
        </div>
      </template>

      <template #cell-status="{ row }">
        <span :class="getStatusClass(row.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ row, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>

          <div
            v-if="showDropdown[index]"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="py-1">
              <button
                @click="viewDetails(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Details
              </button>
              <button
                @click="viewTransactions(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"
              >
                View Transactions
              </button>
              <button
                v-if="row.status === 2"
                @click="approveWithdrawal(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Approve
              </button>
              <button
                v-if="row.status === 2"
                @click="rejectWithdrawal(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Reject
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon,
  EllipsisVerticalIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import { billPaymentsApi } from '@/services/billPaymentsApi'
import { useAuthStore } from '@/stores/auth'

// Router and auth store
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const withdrawals = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showDropdown = reactive<Record<number, boolean>>({})

// Filters
const filters = ref({
  reference_number: '',
  withdrawal_status: '',
  start: '',
  end: '',
  receipt_number: '',
  loan_number: '',
  client_id: '',
  client_phone: ''
})

// Table headers
const tableHeaders = [
  { key: 'reference_number', label: 'Reference No', sortable: true },
  { key: 'receipt_number', label: 'Receipt No', sortable: true },
  { key: 'customer', label: 'Profile Acc', sortable: false },
  { key: 'description', label: 'Description', sortable: false },
  { key: 'amount', label: 'Amount', sortable: true },
  { key: 'charges', label: 'Charges', sortable: true },
  { key: 'created', label: 'Date', sortable: true },
  { key: 'status', label: 'Status', sortable: true }
]

// Methods
const fetchWithdrawals = async () => {
  loading.value = true
  try {
    const response = await billPaymentsApi.getWithdrawals({
      page: currentPage.value,
      limit: pageSize.value,
      ...filters.value
    })

    if (response.status === 200) {
      withdrawals.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      withdrawals.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching withdrawals:', error)
    withdrawals.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchWithdrawals()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchWithdrawals()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchWithdrawals()
}

const handleRowClick = (row: any) => {
  viewDetails(row)
}

const refreshData = () => {
  fetchWithdrawals()
}

const applyFilters = () => {
  currentPage.value = 1
  fetchWithdrawals()
}

const clearFilters = () => {
  filters.value = {
    reference_number: '',
    withdrawal_status: '',
    start: '',
    end: '',
    receipt_number: '',
    loan_number: '',
    client_id: '',
    client_phone: ''
  }
  currentPage.value = 1
  fetchWithdrawals()
}

const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })
  // Toggle current dropdown
  showDropdown[index] = !showDropdown[index]
}

const viewDetails = (withdrawal: any) => {
  console.log('View details for withdrawal:', withdrawal.reference_number)
  // TODO: Navigate to withdrawal details page or open modal
}

const viewTransactions = (withdrawal: any) => {
  router.push({
    name: 'transactions',
    query: { loan_number: withdrawal.loan_number }
  })
}

const approveWithdrawal = async (withdrawal: any) => {
  try {
    const confirmed = confirm(`Are you sure you want to approve withdrawal ${withdrawal.reference_number}?`)
    if (!confirmed) return

    loading.value = true
    // TODO: Implement withdrawal approval API call
    console.log('Approve withdrawal:', withdrawal.reference_number)

    alert('Withdrawal approved successfully')
    fetchWithdrawals() // Refresh the list
  } catch (error) {
    console.error('Error approving withdrawal:', error)
    alert('Failed to approve withdrawal. Please try again.')
  } finally {
    loading.value = false
  }
}

const rejectWithdrawal = async (withdrawal: any) => {
  try {
    const confirmed = confirm(`Are you sure you want to reject withdrawal ${withdrawal.reference_number}?`)
    if (!confirmed) return

    loading.value = true
    // TODO: Implement withdrawal rejection API call
    console.log('Reject withdrawal:', withdrawal.reference_number)

    alert('Withdrawal rejected successfully')
    fetchWithdrawals() // Refresh the list
  } catch (error) {
    console.error('Error rejecting withdrawal:', error)
    alert('Failed to reject withdrawal. Please try again.')
  } finally {
    loading.value = false
  }
}

const exportData = () => {
  console.log('Export withdrawals data')
  // TODO: Implement export functionality
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  try {
    const date = new Date(dateString)
    return date.toLocaleString()
  } catch (error) {
    return 'N/A'
  }
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'Successful',
    0: 'Failed',
    2: 'Pending'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    0: 'bg-red-100 text-red-800',
    2: 'bg-yellow-100 text-yellow-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize data
onMounted(() => {
  fetchWithdrawals()
})
</script>
