<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Add New Client</h1>
        <p class="mt-1 text-sm text-gray-500">
          Create a new client organization
        </p>
      </div>
      <router-link
        :to="{ name: 'clients' }"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <ArrowLeftIcon class="h-4 w-4 mr-2" />
        Back to Clients
      </router-link>
    </div>

    <!-- Add Client Form -->
    <div class="bg-white shadow-sm rounded-lg border border-gray-200">
      <form @submit.prevent="submitForm" class="space-y-6 p-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="client_name" class="block text-sm font-medium text-gray-700">
                Client Name *
              </label>
              <input
                id="client_name"
                v-model="form.client_name"
                type="text"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter client organization name"
              />
              <p v-if="errors.client_name" class="mt-1 text-sm text-red-600">{{ errors.client_name }}</p>
            </div>

            <div>
              <label for="client_email" class="block text-sm font-medium text-gray-700">
                Email Address *
              </label>
              <input
                id="client_email"
                v-model="form.client_email"
                type="email"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter email address"
              />
              <p v-if="errors.client_email" class="mt-1 text-sm text-red-600">{{ errors.client_email }}</p>
            </div>

            <div>
              <label for="client_phone" class="block text-sm font-medium text-gray-700">
                Phone Number *
              </label>
              <input
                id="client_phone"
                v-model="form.client_phone"
                type="tel"
                required
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="254712345678"
              />
              <p v-if="errors.client_phone" class="mt-1 text-sm text-red-600">{{ errors.client_phone }}</p>
            </div>

            <div>
              <label for="client_address" class="block text-sm font-medium text-gray-700">
                Address
              </label>
              <input
                id="client_address"
                v-model="form.client_address"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter physical address"
              />
            </div>
          </div>
        </div>

        <!-- Loan Configuration -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Loan Configuration</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label for="can_issue_loans" class="block text-sm font-medium text-gray-700">
                Can Issue Loans
              </label>
              <select
                id="can_issue_loans"
                v-model="form.can_issue_loans"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="1">Yes</option>
                <option value="0">No</option>
              </select>
            </div>

            <div>
              <label for="service_fee" class="block text-sm font-medium text-gray-700">
                Service Fee (%)
              </label>
              <input
                id="service_fee"
                v-model="form.service_fee"
                type="number"
                step="0.001"
                min="0"
                max="1"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.030"
              />
              <p class="mt-1 text-xs text-gray-500">Enter as decimal (e.g., 0.030 for 3%)</p>
            </div>

            <div>
              <label for="currency_code" class="block text-sm font-medium text-gray-700">
                Currency
              </label>
              <select
                id="currency_code"
                v-model="form.currency_code"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="KES">KES - Kenyan Shilling</option>
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Operating Hours -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Operating Hours</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="open_date" class="block text-sm font-medium text-gray-700">
                Opening Day of Month
              </label>
              <select
                id="open_date"
                v-model="form.open_date"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option v-for="day in 31" :key="day" :value="day.toString()">
                  {{ day }}{{ getDateSuffix(day.toString()) }}
                </option>
              </select>
            </div>

            <div>
              <label for="close_date" class="block text-sm font-medium text-gray-700">
                Closing Day of Month
              </label>
              <select
                id="close_date"
                v-model="form.close_date"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option v-for="day in 31" :key="day" :value="day.toString()">
                  {{ day }}{{ getDateSuffix(day.toString()) }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- Payment Configuration -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Configuration</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label for="b2c_paybill" class="block text-sm font-medium text-gray-700">
                B2C PayBill
              </label>
              <input
                id="b2c_paybill"
                v-model="form.b2c_paybill"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="3037395"
              />
            </div>

            <div>
              <label for="c2b_paybill" class="block text-sm font-medium text-gray-700">
                C2B PayBill
              </label>
              <input
                id="c2b_paybill"
                v-model="form.c2b_paybill"
                type="text"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="4114763"
              />
            </div>

            <div>
              <label for="sender_id" class="block text-sm font-medium text-gray-700">
                SMS Sender ID
              </label>
              <input
                id="sender_id"
                v-model="form.sender_id"
                type="text"
                maxlength="11"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="SaloPlus"
              />
              <p class="mt-1 text-xs text-gray-500">Maximum 11 characters</p>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <router-link
            :to="{ name: 'clients' }"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating...
            </span>
            <span v-else>Create Client</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { clientsApi } from '@/services/clientsApi'
import type { ClientEntity } from '@/services/types'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)

// Form data
const form = reactive<Partial<ClientEntity>>({
  client_name: '',
  client_email: '',
  client_phone: '',
  client_address: '',
  can_issue_loans: '1',
  service_fee: '0.030',
  currency_code: 'KES',
  open_date: '1',
  close_date: '31',
  b2c_paybill: '3037395',
  c2b_paybill: '4114763',
  sender_id: 'SaloPlus'
})

// Form errors
const errors = reactive<Record<string, string>>({})

// Methods
const validateForm = (): boolean => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  let isValid = true

  // Required field validation
  if (!form.client_name?.trim()) {
    errors.client_name = 'Client name is required'
    isValid = false
  }

  if (!form.client_email?.trim()) {
    errors.client_email = 'Email address is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.client_email)) {
    errors.client_email = 'Please enter a valid email address'
    isValid = false
  }

  if (!form.client_phone?.trim()) {
    errors.client_phone = 'Phone number is required'
    isValid = false
  } else if (!/^254\d{9}$/.test(form.client_phone.replace(/\s+/g, ''))) {
    errors.client_phone = 'Please enter a valid Kenyan phone number (254XXXXXXXXX)'
    isValid = false
  }

  return isValid
}

const submitForm = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const response = await clientsApi.addClient(form)

    if (response.status === 200) {
      // Success - redirect to clients list
      router.push({ name: 'clients' })
    } else {
      // Handle API errors
      console.error('Failed to create client:', response.message)
      // You could show a toast notification here
    }
  } catch (error) {
    console.error('Error creating client:', error)
    // You could show a toast notification here
  } finally {
    loading.value = false
  }
}

// Utility functions
const getDateSuffix = (day: string): string => {
  const dayNum = parseInt(day)
  if (dayNum >= 11 && dayNum <= 13) {
    return 'th'
  }
  switch (dayNum % 10) {
    case 1: return 'st'
    case 2: return 'nd'
    case 3: return 'rd'
    default: return 'th'
  }
}
</script>
