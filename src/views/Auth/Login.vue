<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <div class="mx-auto h-20 w-auto flex items-center justify-center">
          <img src="@/assets/logo.png" alt="Mossbets B2B Logo" class="h-20 w-auto" />
        </div>
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Sign in to Mossbets B2B
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Business Dashboard
        </p>
      </div>

      <!-- Login Form -->
      <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <!-- Alert -->
          <div v-if="error" class="mb-4 p-4 rounded-md bg-red-50 border border-red-200">
            <div class="flex">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <div class="ml-3">
                <p class="text-sm text-red-800">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Success Message -->
          <div v-if="successMessage" class="mb-4 p-4 rounded-md bg-green-50 border border-green-200">
            <div class="flex">
              <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              <div class="ml-3">
                <p class="text-sm text-green-800">{{ successMessage }}</p>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <!-- Username -->
            <div>
              <label for="username" class="block text-sm font-medium text-gray-700">
                Username
              </label>
              <input
                id="username"
                v-model="form.username"
                type="text"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your username"
              />
            </div>

            <!-- Password -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700">
                Password
              </label>
              <input
                id="password"
                v-model="form.password"
                type="password"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your password"
              />
            </div>


          </div>

          <!-- Submit Button -->
          <div class="mt-6">
            <button
              type="submit"
              :disabled="loading"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              {{ loading ? 'Signing in...' : 'Sign In' }}
            </button>
          </div>

          <!-- Forgot Password Link -->
          <div class="mt-4 text-center">
            <router-link
              :to="{ name: 'forgot-password' }"
              class="text-sm text-blue-600 hover:text-blue-500 transition-colors duration-200"
            >
              Forgot your password?
            </router-link>
          </div>
        </div>
      </form>

      <!-- Client Selection (if multi-client) -->
      <div v-if="showClientSelection" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Organization</h3>
        <div class="space-y-2">
          <button
            v-for="client in clients"
            :key="client.client_id"
            @click="selectClient(client.client_id)"
            class="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <div class="font-medium text-gray-900">{{ client.client_name }}</div>
            <div class="text-sm text-gray-500">{{ client.client_account }}</div>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Router and store
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const error = ref('')
const successMessage = ref('')

const showClientSelection = ref(false)
const clients = ref<any[]>([])

// Form data
const form = reactive({
  username: '',
  password: '',
  dial_code: '254'
})

// Methods
const handleSubmit = async () => {
  error.value = ''
  successMessage.value = ''
  loading.value = true

  try {
    // Initial login
    const result = await authStore.login(form)

    if (result.success) {
      successMessage.value = 'Login successful! Redirecting...'
      setTimeout(() => {
        router.push({ name: 'dashboard' })
      }, 1000)
    } else if (result.requiresCode) {
      // Set OTP context in store for backup
      const credentials = {
        username: form.username,
        password: btoa(form.password),
        dial_code: form.dial_code
      }
      authStore.setOtpContext(result.data, credentials)

      // Navigate to OTP verification page with context
      router.push({
        name: 'verify-otp',
        params: {
          userData: JSON.stringify(result.data),
          credentials: JSON.stringify(credentials)
        }
      })
    } else {
      error.value = result.message || 'Login failed'
    }
  } catch (err: any) {
    error.value = err.message || 'An unexpected error occurred'
  } finally {
    loading.value = false
  }
}

const selectClient = async (clientId: string) => {
  try {
    await authStore.selectClient(clientId)
    successMessage.value = 'Organization selected! Redirecting...'
    setTimeout(() => {
      router.push({ name: 'dashboard' })
    }, 1000)
  } catch (err: any) {
    error.value = err.message || 'Failed to select organization'
  }
}

// Clear error when user starts typing
const clearError = () => {
  if (error.value) {
    error.value = ''
  }
}

// Watch form changes to clear errors
import { watch } from 'vue'
watch(() => form.username, clearError)
watch(() => form.password, clearError)
</script>
