<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Add System User</h1>
          <p class="text-gray-600 mt-1">Create a new system user account with role and permissions</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Users
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-3 text-gray-600">Loading...</span>
      </div>
    </div>

    <!-- Form -->
    <div v-else class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <form @submit.prevent="createUser" class="space-y-6">
        <!-- Client Selection (for non-super users) -->
        <div v-if="!authStore.isSuperUser" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            Organisation <span v-if="clientName" class="text-gray-500">({{ clientName }})</span>
          </label>
          <div class="relative">
            <input
              v-model="searchClient"
              @click="toggleSearchDropdown"
              :placeholder="searchDropdownPlaceholder"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              readonly
            />
            <div
              v-if="searchDropdown"
              class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
            >
              <div
                v-for="item in organisations"
                :key="item.value"
                @click="setClientId(item)"
                class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100"
              >
                {{ item.text }}
              </div>
            </div>
          </div>
        </div>

        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
              <input
                v-model="userForm.first_name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter first name"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Middle Name</label>
              <input
                v-model="userForm.middle_name"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter middle name"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
              <input
                v-model="userForm.last_name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter last name"
              />
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input
                v-model="userForm.email_address"
                type="email"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter email address"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
              <div class="flex">
                <select
                  v-model="userForm.dial_code"
                  class="block px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-50"
                >
                  <option value="254">+254</option>
                </select>
                <input
                  v-model="userForm.msisdn"
                  type="tel"
                  required
                  class="block w-full px-3 py-2 border-t border-r border-b border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="712345678"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Identity Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Identity Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Nationality *</label>
              <select
                v-model="userForm.nationality"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Select Country</option>
                <option v-for="nationality in nationalities" :key="nationality.value" :value="nationality.value">
                  {{ nationality.text }}
                </option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">ID Type *</label>
              <select
                v-model="userForm.identifier_type"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Select ID Type</option>
                <option v-for="type in identifier_types" :key="type.value" :value="type.value">
                  {{ type.text }}
                </option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">ID Number *</label>
              <input
                v-model="userForm.national_id"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter ID number"
              />
            </div>
          </div>
        </div>

        <!-- Role Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Role & Permissions</h3>
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Assign Role *</label>
              <select
                v-model="userForm.role_id"
                @change="onRoleChange"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Choose a Role</option>
                <option v-for="role in roles" :key="role.role_id" :value="role.role_id">
                  {{ role.role_name }}
                </option>
              </select>
              <p v-if="selectedRole" class="mt-1 text-sm text-gray-500">
                {{ selectedRole.description || 'No description available' }}
              </p>
            </div>

            <!-- Permissions Preview -->
            <div v-if="selectedRole && selectedRole.permissions?.length > 0" class="space-y-4">
              <div>
                <h4 class="text-sm font-medium text-gray-900 mb-2">Role Permissions (Auto-selected)</h4>
                <div class="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div
                      v-for="permission in selectedRole.permissions"
                      :key="permission.id"
                      class="flex items-center text-sm text-gray-700"
                    >
                      <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                      {{ permission.name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading || !isFormValid"
            class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <div v-if="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Create User
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { systemApi, type Role, type Permission } from '@/services/systemApi'
import { useAuthStore } from '@/stores/auth'

// Router and stores
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const roles = ref<Role[]>([])

// Form state
const selectedRole = ref('')

// User form
const userForm = ref({
  dial_code: '254',
  msisdn: '',
  email_address: '',
  first_name: '',
  middle_name: '',
  last_name: '',
  role_id: '',
  national_id: '',
  nationality: 'KENYA',
  identifier_type: 'NATIONAL_ID',
  account_number: ''
})

// Static data
const nationalities = [
  { text: 'KENYA', value: 'KENYA' }
]

const identifier_types = [
  { text: 'NATIONAL ID', value: 'NATIONAL_ID' },
  { text: 'HUDUMA ID', value: 'HUDUMA_ID' },
  { text: 'PASSPORT', value: 'PASSPORT' },
  { text: 'ALIEN ID', value: 'ALIEN_ID' }
]

// Computed properties
const selectedRole = computed(() => {
  return roles.value.find(role => role.role_id === parseInt(userForm.value.role_id))
})

const isFormValid = computed(() => {
  return userForm.value.first_name &&
         userForm.value.last_name &&
         userForm.value.email_address &&
         userForm.value.msisdn &&
         userForm.value.role_id &&
         userForm.value.national_id &&
         userForm.value.nationality &&
         userForm.value.identifier_type
})

// Methods
const setClientId = (item: any) => {
  clientName.value = item.text
  userForm.value.account_number = item.value
  searchDropdown.value = false
  searchClient.value = ''
  searchDropdownPlaceholder.value = clientName.value
}

const toggleSearchDropdown = () => {
  searchDropdown.value = !searchDropdown.value
}

const onRoleChange = () => {
  // When role changes, permissions are automatically handled by the selectedRole computed property
  // The API will handle permission assignment based on the role
}

const fetchOrganisations = async () => {
  // Organizations functionality removed as per requirements
}

const fetchRoles = async () => {
  try {
    const response = await systemApi.getRoles({ limit: 100 })
    if (response.status === 200) {
      roles.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching roles:', error)
    roles.value = []
  }
}

const createUser = async () => {
  if (!isFormValid.value) {
    alert('Please fill in all required fields')
    return
  }

  if (!confirm('Are you sure you want to create this user?')) {
    return
  }

  loading.value = true
  try {
    // Prepare payload with role permissions
    const payload = {
      username: `${userForm.value.first_name}_${userForm.value.last_name}`.toLowerCase(),
      email_address: userForm.value.email_address,
      msisdn: userForm.value.msisdn,
      role_id: parseInt(userForm.value.role_id),
      permissions: selectedRole.value?.permissions?.map(p => p.id) || [],
      client_id: userForm.value.account_number || undefined,
      first_name: userForm.value.first_name,
      middle_name: userForm.value.middle_name,
      last_name: userForm.value.last_name,
      national_id: userForm.value.national_id,
      nationality: userForm.value.nationality,
      identifier_type: userForm.value.identifier_type
    }

    const response = await systemApi.createUser(payload)
    if (response.status === 200) {
      alert('User created successfully!')
      router.push({ name: 'system-users' })
    } else {
      alert('Error creating user: ' + response.message)
    }
  } catch (error) {
    console.error('Error creating user:', error)
    alert('Error creating user')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-users' })
}

// Initialize data on mount
onMounted(async () => {
  if (!authStore.isSuperUser) {
    userForm.value.account_number = authStore.user?.client_account || ''
  }

  await Promise.all([
    fetchOrganisations(),
    fetchRoles()
  ])

  initialLoading.value = false
})
</script>
