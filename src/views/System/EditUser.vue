<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Edit User</h1>
          <p class="text-gray-600 mt-1">Update user information, roles and permissions</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Users
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-2 text-sm text-gray-500">Loading user data...</p>
    </div>

    <!-- Form -->
    <div v-else class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <form @submit.prevent="saveUser" class="space-y-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Username *</label>
              <input
                v-model="userForm.username"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter username"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input
                v-model="userForm.email_address"
                type="email"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter email address"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
              <input
                v-model="userForm.msisdn"
                type="tel"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter phone number"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                v-model="userForm.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option :value="1">Active</option>
                <option :value="0">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Role Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Role Assignment</h3>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Select Role *</label>
            <select
              v-model="userForm.role_id"
              required
              @change="onRoleChange"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">Select a role</option>
              <option v-for="role in roles" :key="role.role_id" :value="role.role_id">
                {{ role.role_name }}
              </option>
            </select>
            <p v-if="selectedRole" class="mt-1 text-sm text-gray-500">
              {{ selectedRole.description }}
            </p>
          </div>
        </div>

        <!-- Permissions -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm font-medium text-gray-700">
                {{ userForm.permissions.length }} permissions selected
              </span>
              <div class="flex space-x-2">
                <button
                  type="button"
                  @click="selectAllPermissions"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  Select All
                </button>
                <button
                  type="button"
                  @click="clearAllPermissions"
                  class="text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear All
                </button>
              </div>
            </div>
            
            <div class="max-h-60 overflow-y-auto space-y-4">
              <div v-for="(modulePermissions, module) in groupedPermissions" :key="module" class="border border-gray-200 rounded-lg">
                <div class="px-3 py-2 bg-white border-b border-gray-200 rounded-t-lg">
                  <div class="flex items-center">
                    <input
                      :id="`module-${module}`"
                      type="checkbox"
                      :checked="isModuleSelected(modulePermissions)"
                      @change="toggleModule(modulePermissions)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`module-${module}`" class="ml-2 text-sm font-medium text-gray-900 capitalize">
                      {{ formatModuleName(module) }}
                    </label>
                  </div>
                </div>
                <div class="px-3 py-2 space-y-2">
                  <div v-for="permission in modulePermissions" :key="permission.id" class="flex items-center">
                    <input
                      :id="`permission-${permission.id}`"
                      v-model="userForm.permissions"
                      :value="permission.id"
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`permission-${permission.id}`" class="ml-2 text-sm text-gray-700">
                      {{ permission.name }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Client Assignment -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Client Assignment</h3>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Assign Client (Optional)</label>
            <select
              v-model="userForm.client_id"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">No client assigned</option>
              <option v-for="client in clients" :key="client.client_id" :value="client.client_id">
                {{ client.client_name }}
              </option>
            </select>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Updating...' : 'Update User' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { systemApi, type Role, type Permission, type SystemUser } from '@/services/systemApi'
import { clientsApi } from '@/services/clientsApi'

// Router
const router = useRouter()
const route = useRoute()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const roles = ref<Role[]>([])
const permissions = ref<Permission[]>([])
const clients = ref<any[]>([])
const currentUser = ref<SystemUser | null>(null)

// User form
const userForm = ref({
  user_id: '',
  username: '',
  email_address: '',
  msisdn: '',
  role_id: '',
  permissions: [] as number[],
  client_id: '',
  status: 1
})

// Computed properties
const selectedRole = computed(() => {
  return roles.value.find(role => role.role_id === parseInt(userForm.value.role_id))
})

const groupedPermissions = computed(() => {
  const grouped: Record<string, Permission[]> = {}
  
  permissions.value.forEach(permission => {
    const module = permission.module || 'general'
    if (!grouped[module]) {
      grouped[module] = []
    }
    grouped[module].push(permission)
  })
  
  return grouped
})

// Methods
const fetchUser = async () => {
  const userId = route.params.id as string
  if (!userId) {
    router.push({ name: 'system-roles' })
    return
  }

  try {
    // This would need a specific API endpoint to get a single user
    // For now, we'll fetch all users and find the one we need
    const response = await systemApi.getUsers({ limit: 1000 })
    if (response.status === 200) {
      const users = response.message?.data || []
      currentUser.value = users.find(user => user.user_id === userId) || null
      
      if (currentUser.value) {
        userForm.value = {
          user_id: currentUser.value.user_id,
          username: currentUser.value.username,
          email_address: currentUser.value.email_address,
          msisdn: currentUser.value.msisdn,
          role_id: currentUser.value.role_id.toString(),
          permissions: currentUser.value.permissions?.map(p => p.id) || [],
          client_id: currentUser.value.client_id || '',
          status: currentUser.value.status
        }
      } else {
        router.push({ name: 'system-users' })
      }
    }
  } catch (error) {
    console.error('Error fetching user:', error)
    router.push({ name: 'system-users' })
  } finally {
    initialLoading.value = false
  }
}

const fetchRoles = async () => {
  try {
    const response = await systemApi.getRoles({ limit: 100 })
    if (response.status === 200) {
      roles.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching roles:', error)
  }
}

const fetchPermissions = async () => {
  try {
    const response = await systemApi.getPermissions({ limit: 100 })
    if (response.status === 200) {
      permissions.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
  }
}

const fetchClients = async () => {
  try {
    const response = await clientsApi.getClients({ limit: 100 })
    if (response.status === 200) {
      clients.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching clients:', error)
  }
}

const onRoleChange = () => {
  if (selectedRole.value) {
    // Auto-select role permissions
    userForm.value.permissions = selectedRole.value.permissions?.map(p => p.id) || []
  }
}

const isModuleSelected = (modulePermissions: Permission[]) => {
  return modulePermissions.every(permission => 
    userForm.value.permissions.includes(permission.id)
  )
}

const toggleModule = (modulePermissions: Permission[]) => {
  const allSelected = isModuleSelected(modulePermissions)
  
  if (allSelected) {
    // Remove all permissions from this module
    modulePermissions.forEach(permission => {
      const index = userForm.value.permissions.indexOf(permission.id)
      if (index > -1) {
        userForm.value.permissions.splice(index, 1)
      }
    })
  } else {
    // Add all permissions from this module
    modulePermissions.forEach(permission => {
      if (!userForm.value.permissions.includes(permission.id)) {
        userForm.value.permissions.push(permission.id)
      }
    })
  }
}

const selectAllPermissions = () => {
  userForm.value.permissions = permissions.value.map(p => p.id)
}

const clearAllPermissions = () => {
  userForm.value.permissions = []
}

const saveUser = async () => {
  loading.value = true
  try {
    const response = await systemApi.updateUser({
      user_id: userForm.value.user_id,
      username: userForm.value.username,
      email_address: userForm.value.email_address,
      msisdn: userForm.value.msisdn,
      role_id: parseInt(userForm.value.role_id),
      permissions: userForm.value.permissions,
      status: userForm.value.status
    })
    
    if (response.status === 200) {
      router.push({ name: 'system-roles' })
    } else {
      console.error('Failed to update user:', response.message)
    }
  } catch (error) {
    console.error('Error updating user:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-roles' })
}

const formatModuleName = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

// Initialize data
onMounted(async () => {
  await Promise.all([
    fetchRoles(),
    fetchPermissions(),
    fetchClients()
  ])
  await fetchUser()
})
</script>
