<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Loan Requests</h1>
          <p class="text-gray-600 mt-1">Manage and review loan requests from customers</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filter Cards -->
    <FilterCards
      :filters="filters"
      filter-type="requests"
      :status-options="statusOptions"
      @update:filters="updateFilters"
      @apply="applyFilters"
      @clear="clearFilters"
    />

    

    <!-- Loan Requests Data Table -->
    <DataTable
      :data="loanRequests"
      :header="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Loan Requests"
      row-key="req_number"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-mobile="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">+{{ item.mobile }}</div>
          <div class="text-gray-500">{{ item.name }}</div>
          <div class="text-xs text-gray-400">{{ item.merchant_name }}</div>
        </div>
      </template>

      <template #cell-product_name="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ item.product_name }}</div>
          <div class="text-xs text-gray-500">{{ (parseFloat(item.interest_charged) * 100).toFixed(1) }}% interest</div>
        </div>
      </template>

      <template #cell-amounts="{ item }">
        <div class="text-sm text-center">
          <div class="font-medium text-gray-900">Req: {{ formatCurrency(item.requested_amount) }}</div>
          <div class="text-green-600">App: {{ formatCurrency(item.approved_amount) }}</div>
        </div>
      </template>

      <template #cell-status="{ item }">
        <span :class="getStatusClass(item.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ getStatusText(item.status) }}
        </span>
      </template>

      <template #cell-created_at="{ item }">
        <div class="text-sm text-gray-900">
          {{ formatDate(item.created_at) }}
        </div>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ item, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>

          <div
            v-if="showDropdown[index]"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="py-1">
              <button
                @click="viewDetails(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Details
              </button>
              <button
                v-if="item.status === 2 || item.status === 5 || item.status === 4"
                @click="approveRequest(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Approve
              </button>
              <button
                v-if="item.status === 2 || item.status === 5 || item.status === 4"
                @click="rejectRequest(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Reject
              </button>
              <button
                v-if="item.status === 4"
                @click="resendTAN(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-900"
              >
                Resend Code
              </button>
              <button
                @click="viewRepayments(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"
              >
                View Repayments
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon,
  EllipsisVerticalIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import FilterCards from '@/components/FilterCards.vue'
import { loanApi, type LoanRequest } from '@/services/loanApi'
import { useAuthStore } from '@/stores/auth'
import { useBackofficeActions } from '@/composables/useBackofficeActions'

// Router and auth store
const router = useRouter()
const authStore = useAuthStore()

// Use backoffice actions composable
const {
  showDropdown,
  loading,
  currentPage,
  pageSize,
  totalRecords,
  searchQuery,
  toggleDropdown,
  closeDropdown,
  goToPage,
  editRow,
  viewRelatedData,
  handleSearch: handleSearchAction
} = useBackofficeActions()

// Reactive data
const loanRequests = ref<LoanRequest[]>([])
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Filters
const filters = ref({
  client_id: authStore.selectedClientId || '',
  status: '',
  loan_number: '',
  reference_id: '',
  phone_number: '',
  start_date: '',
  end_date: '',
  amount: ''
})

// Status options for filters
const statusOptions = [
  { value: '1', label: 'Approved' },
  { value: '2', label: 'Pending' },
  { value: '3', label: 'Rejected' },
  { value: '4', label: 'Awaiting TAN' },
  { value: '5', label: 'Disbursed' },
  { value: '6', label: 'Completed' }
]

// Table headers
const tableHeaders = [
  { key: 'req_number', label: 'Request No', sortable: true },
  { key: 'mobile', label: 'Customer', sortable: false },
  { key: 'product_name', label: 'Product', sortable: true },
  { key: 'amounts', label: 'Amounts', sortable: false },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Date', sortable: true }
]

// Methods
const fetchLoanRequests = async () => {
  loading.value = true
  try {
    const response = await loanApi.getLoanRequests({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      ...filters.value
    })

    if (response.status === 200) {
      loanRequests.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      loanRequests.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching loan requests:', error)
    loanRequests.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

// Filter methods
const updateFilters = (newFilters: Record<string, any>) => {
  Object.assign(filters.value, newFilters)
}

const handlePageChange = (page: number) => {
  goToPage(page, fetchLoanRequests)
}

const handleSearch = (query: string) => {
  handleSearchAction(query, fetchLoanRequests)
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchLoanRequests()
}

const handleRowClick = (row: LoanRequest) => {
  viewDetails(row)
}

const refreshData = () => {
  fetchLoanRequests()
}

const applyFilters = (newFilters: Record<string, any>) => {
  filters.value = { ...newFilters }
  currentPage.value = 1
  fetchLoanRequests()
}

const clearFilters = () => {
  filters.value = {
    client_id: authStore.selectedClientId || '',
    status: '',
    loan_number: '',
    reference_id: '',
    phone_number: '',
    start_date: '',
    end_date: '',
    amount: ''
  }
  currentPage.value = 1
  fetchLoanRequests()
}



const viewDetails = (request: LoanRequest) => {
  console.log('View details for request:', request.req_number)
  // TODO: Navigate to request details page or open modal
}

const approveRequest = async (request: LoanRequest) => {
  try {
    const confirmed = confirm(`Are you sure you want to approve loan request ${request.req_number} for ${formatCurrency(request.requested_amount)}?`)
    if (!confirmed) return

    loading.value = true
    const response = await loanApi.approveLoanRequest(
      request.req_number,
      1, // Status 1 = Approved
      parseFloat(request.requested_amount.toString()),
      `Loan of ${request.requested_amount} approved`
    )

    if (response.status === 200) {
      alert('Loan request approved successfully')
      fetchLoanRequests() // Refresh the list
    } else {
      alert(`Failed to approve loan request: ${response.message}`)
    }
  } catch (error) {
    console.error('Error approving loan request:', error)
    alert('Failed to approve loan request. Please try again.')
  } finally {
    loading.value = false
  }
}

const rejectRequest = async (request: LoanRequest) => {
  try {
    const confirmed = confirm(`Are you sure you want to reject loan request ${request.req_number}?`)
    if (!confirmed) return

    loading.value = true
    const response = await loanApi.approveLoanRequest(
      request.req_number,
      0, // Status 0 = Rejected
      0,
      `Loan request rejected`
    )

    if (response.status === 200) {
      alert('Loan request rejected successfully')
      fetchLoanRequests() // Refresh the list
    } else {
      alert(`Failed to reject loan request: ${response.message}`)
    }
  } catch (error) {
    console.error('Error rejecting loan request:', error)
    alert('Failed to reject loan request. Please try again.')
  } finally {
    loading.value = false
  }
}

const resendTAN = async (request: LoanRequest) => {
  try {
    const confirmed = confirm(`Resend TAN code for loan request ${request.req_number}?`)
    if (!confirmed) return

    loading.value = true
    const response = await loanApi.resendTAN(request.req_number)

    if (response.status === 200) {
      alert('TAN code sent successfully')
    } else {
      alert(`Failed to send TAN code: ${response.message}`)
    }
  } catch (error) {
    console.error('Error sending TAN:', error)
    alert('Failed to send TAN code. Please try again.')
  } finally {
    loading.value = false
  }
}

const viewRepayments = (request: LoanRequest) => {
  router.push({
    name: 'loan-repayments',
    query: { loan_number: request.req_number }
  })
}

const exportData = () => {
  console.log('Export loan requests data')
  // TODO: Implement export functionality
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(num)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'Approved',
    2: 'Pending',
    3: 'Rejected',
    4: 'Awaiting TAN',
    5: 'Disbursed',
    6: 'Completed'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    2: 'bg-yellow-100 text-yellow-800',
    3: 'bg-red-100 text-red-800',
    4: 'bg-purple-100 text-purple-800',
    5: 'bg-blue-100 text-blue-800',
    6: 'bg-gray-100 text-gray-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize data
onMounted(() => {
  fetchLoanRequests()
})
</script>
