<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Loan Accounts</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage customer loan accounts for {{ selectedClient?.client_name || 'All Organizations' }}
        </p>
      </div>
      <button
        v-if="canAddAccounts"
        @click="showAddModal = true"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
      >
        <PlusIcon class="h-4 w-4 mr-2" />
        Add Loan Account
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <UserGroupIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Accounts</dt>
                <dd class="text-lg font-medium text-gray-900">{{ totalRecords }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-green-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                <dd class="text-lg font-medium text-gray-900">{{ activeCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-yellow-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-yellow-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Unverified</dt>
                <dd class="text-lg font-medium text-gray-900">{{ unverifiedCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-6 w-6 bg-red-100 rounded-full flex items-center justify-center">
                <div class="h-3 w-3 bg-red-500 rounded-full"></div>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Suspended</dt>
                <dd class="text-lg font-medium text-gray-900">{{ suspendedCount }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CurrencyDollarIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Limits</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(totalLimits) }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Filter Type</label>
          <select
            v-model="selectedFilter"
            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select filter type</option>
            <option v-for="filter in filterOptions" :key="filter.value" :value="filter">
              {{ filter.text }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ selectedFilter?.text || 'Filter Value' }}
          </label>
          <input
            v-if="selectedFilter?.value === 'phone' || selectedFilter?.value === 'email' || selectedFilter?.value === 'loan_number'"
            v-model="filterValue"
            type="text"
            :placeholder="`Enter ${selectedFilter?.text || 'value'}`"
            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            @keyup.enter="applyFilters"
          />
          <select
            v-else-if="selectedFilter?.value === 'organization'"
            v-model="filterValue"
            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select organization</option>
            <option v-for="org in organizations" :key="org.client_id" :value="org.client_id">
              {{ org.client_name }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select
            v-model="statusFilter"
            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            @change="applyFilters"
          >
            <option value="">All Statuses</option>
            <option value="1000">Active</option>
            <option value="1002">New</option>
            <option value="1003">Unverified</option>
            <option value="1004">Dormant</option>
            <option value="1005">Suspended</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
          <input
            type="date"
            v-model="dateRange.start"
            class="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
            @change="applyFilters"
          />
        </div>
      </div>

      <div class="mt-4 flex justify-end space-x-3">
        <button
          @click="clearFilters"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Clear Filters
        </button>
        <button
          @click="applyFilters"
          class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
        >
          Apply Filters
        </button>
      </div>
    </div>

    <!-- Filter Cards -->
    <FilterCards
      :filters="filters"
      filter-type="accounts"
      :status-options="statusOptions"
      @update:filters="updateFilters"
      @apply="applyFilters"
      @clear="clearFilters"
    />

    <!-- Loan Accounts Data Table -->
    <DataTable
      :data="loanAccounts"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Loan Accounts"
      row-key="user_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <button
          @click="refreshData"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
      </template>

      <!-- Custom Cell: Customer Name -->
      <template #cell-first_name="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ item.first_name }} {{ item.last_name }}</div>
          <div class="text-gray-500">{{ item.email_address }}</div>
        </div>
      </template>

      <!-- Custom Cell: Loan Account Number -->
      <template #cell-loan_number="{ value }">
        <div class="font-medium text-blue-600">{{ value }}</div>
      </template>

      <!-- Custom Cell: Phone -->
      <template #cell-msisdn="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">+{{ item.msisdn }}</div>
          <div class="text-gray-500">{{ item.network }}</div>
        </div>
      </template>

      <!-- Custom Cell: Document Info -->
      <template #cell-nationality="{ item }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ item.nationality }} | {{ item.national_id }}</div>
          <div class="text-gray-500">{{ item.gender }} | {{ formatDate(item.dob) }}</div>
        </div>
      </template>

      <!-- Custom Cell: Max Limit -->
      <template #cell-max_approved_loan_amount="{ value }">
        <div class="text-sm font-medium text-green-600">
          KES {{ formatCurrency(value) }}
        </div>
      </template>

      <!-- Custom Cell: Wallet Balance -->
      <template #cell-actual_balance="{ item }">
        <div class="text-sm">
          <div class="text-gray-900">Actual: {{ formatCurrency(item.actual_balance) }}</div>
          <div class="text-gray-500">Loan: {{ formatCurrency(item.loan_balance) }}</div>
        </div>
      </template>

      <!-- Custom Cell: Status -->
      <template #cell-status="{ value }">
        <span :class="[
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          getStatusClass(parseInt(value))
        ]">
          {{ getStatusText(parseInt(value)) }}
        </span>
      </template>

      <!-- Custom Cell: Created Date -->
      <template #cell-created="{ value }">
        <div class="text-sm text-gray-900">
          {{ formatDate(value) }}
        </div>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item, index }">
        <div class="relative" v-if="canEditAccounts && (parseInt(item.status) === 1000 || parseInt(item.status) === 1005)">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            v-if="showDropdown[index]"
            class="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10"
          >
            <div class="py-1">
              <button
                v-if="parseInt(item.status) === 1000"
                @click="suspendAccount(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Suspend {{ item.first_name }}
              </button>
              <button
                v-else-if="parseInt(item.status) === 1005"
                @click="activateAccount(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Activate {{ item.first_name }}
              </button>
              <button
                @click="editAccount(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Edit Account
              </button>
              <button
                @click="viewTransactions(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                View Transactions
              </button>
              <button
                @click="checkKYC(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Check KYC
              </button>
              <button
                v-if="parseInt(item.black_list_state) === 0"
                @click="blacklistAccount(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-yellow-50 hover:text-yellow-900"
              >
                Blacklist
              </button>
              <button
                v-else
                @click="unblockAccount(item)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"
              >
                Unblock
              </button>
            </div>
          </div>
        </div>
        <div v-else class="text-gray-400">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
          </svg>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { PlusIcon, UserGroupIcon, CurrencyDollarIcon } from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import FilterCards from '@/components/FilterCards.vue'
import { loanApi } from '@/services/loanApi'
import { useAuthStore } from '@/stores/auth'
import { useBackofficeActions } from '@/composables/useBackofficeActions'

// Types
interface LoanAccount {
  user_id: string
  loan_number: string
  first_name: string
  last_name: string
  email_address: string
  msisdn: string
  network: string
  nationality: string
  national_id: string
  gender: string
  dob: string
  max_approved_loan_amount: number
  actual_balance: number
  loan_balance: number
  status: number
  black_list_state: number
  created: string
}

interface Organization {
  client_id: string
  client_name: string
}

interface FilterOption {
  text: string
  value: string
}

// Auth store
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const loanAccounts = ref<LoanAccount[]>([])
const organizations = ref<Organization[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)
const showDropdown = reactive<Record<number, boolean>>({})

// Filter data
const selectedFilter = ref<FilterOption | null>(null)
const filterValue = ref('')
const statusFilter = ref('')
const dateRange = ref({
  start: '',
  end: ''
})

// Filters for LoanFilters component
const filters = ref({
  client_id: authStore.selectedClientId || '',
  status: '',
  loan_number: '',
  msisdn: '',
  national_id: '',
  start_date: '',
  end_date: ''
})

// Status options for filters
const statusOptions = [
  { value: '1000', label: 'Active' },
  { value: '1001', label: 'Inactive' },
  { value: '1002', label: 'Suspended' },
  { value: '1003', label: 'Blacklisted' }
]

const filterOptions: FilterOption[] = [
  { text: 'Organization Name', value: 'organization' },
  { text: 'Loan Number', value: 'loan_number' },
  { text: 'Email', value: 'email' },
  { text: 'Phone Number', value: 'phone' }
]

// Table headers configuration
const tableHeaders = {
  first_name: 'Customer Name',
  loan_number: 'Loan Account No',
  msisdn: 'Phone',
  nationality: 'Document Type',
  max_approved_loan_amount: 'Max Limit',
  actual_balance: 'Wallet',
  status: 'Status',
  created: 'Date'
}

// Computed properties
const selectedClient = computed(() => authStore.selectedClient)
const canAddAccounts = computed(() => authStore.hasPermission('loan_accounts_create'))
const canEditAccounts = computed(() => authStore.hasPermission('loan_accounts_edit'))

const activeCount = computed(() =>
  loanAccounts.value.filter(account => account.status === 1000).length
)

const unverifiedCount = computed(() =>
  loanAccounts.value.filter(account => account.status === 1003).length
)

const suspendedCount = computed(() =>
  loanAccounts.value.filter(account => account.status === 1005).length
)

const totalLimits = computed(() =>
  loanAccounts.value.reduce((sum, account) => sum + account.max_approved_loan_amount, 0)
)

// Methods
const fetchLoanAccounts = async () => {
  loading.value = true
  try {
    const response = await loanApi.getLoanAccounts({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value,
      client_id: filters.value.client_id,
      loan_number: filters.value.loan_number,
      msisdn: filters.value.msisdn,
      national_id: filters.value.national_id,
      start_date: filters.value.start_date,
      end_date: filters.value.end_date
    })

    if (response.status === 200) {
      loanAccounts.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      loanAccounts.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching loan accounts:', error)
    loanAccounts.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchLoanAccounts()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchLoanAccounts()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  currentPage.value = 1
  fetchLoanAccounts()
}

const handleRowClick = (item: LoanAccount) => {
  console.log('Row clicked:', item)
}

const refreshData = () => {
  fetchLoanAccounts()
}

// Filter methods
const updateFilters = (newFilters: Record<string, any>) => {
  Object.assign(filters.value, newFilters)
}

const applyFilters = (newFilters?: Record<string, any>) => {
  if (newFilters) {
    filters.value = { ...newFilters }
  }
  currentPage.value = 1
  fetchLoanAccounts()
}

const clearFilters = () => {
  filters.value = {
    client_id: authStore.selectedClientId || '',
    status: '',
    loan_number: '',
    msisdn: '',
    national_id: '',
    start_date: '',
    end_date: ''
  }
  currentPage.value = 1
  fetchLoanAccounts()
}



const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })

  // Toggle current dropdown
  showDropdown[index] = !showDropdown[index]
}

// Account actions
const suspendAccount = async (account: LoanAccount) => {
  try {
    loading.value = true
    // TODO: API call to suspend account
    console.log('Suspending account:', account.loan_number)
    await fetchLoanAccounts()
  } catch (error) {
    console.error('Error suspending account:', error)
  } finally {
    loading.value = false
    showDropdown[loanAccounts.value.indexOf(account)] = false
  }
}

const activateAccount = async (account: LoanAccount) => {
  try {
    loading.value = true
    // TODO: API call to activate account
    console.log('Activating account:', account.loan_number)
    await fetchLoanAccounts()
  } catch (error) {
    console.error('Error activating account:', error)
  } finally {
    loading.value = false
    showDropdown[loanAccounts.value.indexOf(account)] = false
  }
}

const editAccount = (account: LoanAccount) => {
  console.log('Edit account:', account.loan_number)
  showDropdown[loanAccounts.value.indexOf(account)] = false
}

const viewTransactions = (account: LoanAccount) => {
  console.log('View transactions for:', account.loan_number)
  showDropdown[loanAccounts.value.indexOf(account)] = false
}

const checkKYC = (account: LoanAccount) => {
  console.log('Check KYC for:', account.loan_number)
  showDropdown[loanAccounts.value.indexOf(account)] = false
}

const blacklistAccount = (account: LoanAccount) => {
  console.log('Blacklist account:', account.loan_number)
  showDropdown[loanAccounts.value.indexOf(account)] = false
}

const unblockAccount = (account: LoanAccount) => {
  console.log('Unblock account:', account.loan_number)
  showDropdown[loanAccounts.value.indexOf(account)] = false
}

// Utility functions
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatDate = (date: string): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusClass = (status: number): string => {
  switch (status) {
    case 1000:
      return 'bg-green-100 text-green-800'
    case 1002:
      return 'bg-blue-100 text-blue-800'
    case 1003:
      return 'bg-yellow-100 text-yellow-800'
    case 1004:
      return 'bg-gray-100 text-gray-800'
    case 1005:
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: number): string => {
  switch (status) {
    case 1000:
      return 'Active'
    case 1002:
      return 'New'
    case 1003:
      return 'Unverified'
    case 1004:
      return 'Dormant'
    case 1005:
      return 'Suspended'
    default:
      return 'Unknown'
  }
}

// Lifecycle
onMounted(() => {
  fetchLoanAccounts()
})
</script>
