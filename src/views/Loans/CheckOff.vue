<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Loan Check-Off</h1>
          <p class="text-gray-600 mt-1">Manage salary deduction for loan repayments</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filter Cards -->
    <FilterCards
      :filters="filters"
      filter-type="checkoff"
      :status-options="statusOptions"
      @update:filters="updateFilters"
      @apply="applyFilters"
      @clear="clearFilters"
    />

    <!-- Check-Off Data Table -->
    <DataTable
      :data="checkOffReports"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Check-Off Reports"
      row-key="loan_number"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-customer="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.first_name }} {{ row.last_name }}</div>
          <div class="text-gray-500">{{ row.msisdn }}</div>
          <div class="text-xs text-gray-400">{{ row.employee_number }}</div>
        </div>
      </template>

      <template #cell-loan_number="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ row.loan_number }}
        </div>
      </template>

      <template #cell-max_approved_loan_amount="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ formatCurrency(row.max_approved_loan_amount) }}
        </div>
      </template>

      <template #cell-actual_balance="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ formatCurrency(row.actual_balance) }}
        </div>
      </template>

      <template #cell-status="{ row }">
        <span :class="getStatusClass(row.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ row, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>

          <div
            v-if="showDropdown[index]"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="py-1">
              <button
                @click="viewDetails(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Details
              </button>
              <button
                @click="editAccount(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"
              >
                Edit Account
              </button>
              <button
                @click="viewTransactions(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-900"
              >
                View Transactions
              </button>
              <button
                v-if="row.status === 1000"
                @click="blockAccount(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Block Account
              </button>
              <button
                v-if="row.black_list_state === 1"
                @click="unblockAccount(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Unblock Account
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon,
  EllipsisVerticalIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import FilterCards from '@/components/FilterCards.vue'
import { loanApi } from '@/services/loanApi'
import { useAuthStore } from '@/stores/auth'
import { useBackofficeActions } from '@/composables/useBackofficeActions'

// Router and auth store
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const loading = ref(false)
const checkOffReports = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showDropdown = reactive<Record<number, boolean>>({})

// Filters
const filters = ref({
  client_id: authStore.selectedClientId || '',
  status: '',
  type_id: '',
  start_date: '',
  end_date: ''
})

// Status options for filters
const statusOptions = [
  { value: '1000', label: 'Active' },
  { value: '1002', label: 'New' },
  { value: '1003', label: 'Unverified' },
  { value: '1004', label: 'Suspended' },
  { value: '1005', label: 'Dormant' }
]

// Table headers
const tableHeaders = [
  { key: 'customer', label: 'Customer', sortable: false },
  { key: 'loan_number', label: 'Loan Account', sortable: true },
  { key: 'max_approved_loan_amount', label: 'Max Limit', sortable: true },
  { key: 'actual_balance', label: 'Wallet Balance', sortable: true },
  { key: 'status', label: 'Status', sortable: true }
]

// Methods
const fetchCheckOffReports = async () => {
  loading.value = true
  try {
    const response = await loanApi.getCheckOffReports({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      ...filters.value
    })

    if (response.status === 200) {
      checkOffReports.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      checkOffReports.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching check-off reports:', error)
    checkOffReports.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchCheckOffReports()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  fetchCheckOffReports()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchCheckOffReports()
}

const handleRowClick = (row: any) => {
  viewDetails(row)
}

// Filter methods
const updateFilters = (newFilters: Record<string, any>) => {
  Object.assign(filters.value, newFilters)
}

const refreshData = () => {
  fetchCheckOffReports()
}

const applyFilters = (newFilters: Record<string, any>) => {
  filters.value = { ...newFilters }
  currentPage.value = 1
  fetchCheckOffReports()
}

const clearFilters = () => {
  filters.value = {
    client_id: authStore.selectedClientId || '',
    status: '',
    type_id: '',
    start_date: '',
    end_date: ''
  }
  currentPage.value = 1
  fetchCheckOffReports()
}

const toggleDropdown = (index: number) => {
  // Close all other dropdowns
  Object.keys(showDropdown).forEach(key => {
    if (parseInt(key) !== index) {
      showDropdown[parseInt(key)] = false
    }
  })
  // Toggle current dropdown
  showDropdown[index] = !showDropdown[index]
}

const viewDetails = (account: any) => {
  console.log('View details for account:', account.loan_number)
  // TODO: Navigate to account details page or open modal
}

const editAccount = (account: any) => {
  router.push({
    name: 'loan-accounts-edit',
    params: { id: account.loan_number }
  })
}

const viewTransactions = (account: any) => {
  router.push({
    name: 'transactions',
    query: { loan_number: account.loan_number }
  })
}

const blockAccount = async (account: any) => {
  try {
    const reason = prompt('Please enter reason for blocking this account:')
    if (!reason) return

    const confirmed = confirm(`Are you sure you want to block account ${account.loan_number}?`)
    if (!confirmed) return

    loading.value = true
    // TODO: Implement block account API call
    console.log('Block account:', account.loan_number, 'Reason:', reason)

    alert('Account blocked successfully')
    fetchCheckOffReports() // Refresh the list
  } catch (error) {
    console.error('Error blocking account:', error)
    alert('Failed to block account. Please try again.')
  } finally {
    loading.value = false
  }
}

const unblockAccount = async (account: any) => {
  try {
    const confirmed = confirm(`Are you sure you want to unblock account ${account.loan_number}?`)
    if (!confirmed) return

    loading.value = true
    // TODO: Implement unblock account API call
    console.log('Unblock account:', account.loan_number)

    alert('Account unblocked successfully')
    fetchCheckOffReports() // Refresh the list
  } catch (error) {
    console.error('Error unblocking account:', error)
    alert('Failed to unblock account. Please try again.')
  } finally {
    loading.value = false
  }
}

const exportData = () => {
  console.log('Export check-off reports data')
  // TODO: Implement export functionality
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(num)
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1000: 'Active',
    1002: 'New',
    1003: 'Unverified',
    1004: 'Suspended',
    1005: 'Dormant'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1000: 'bg-green-100 text-green-800',
    1002: 'bg-blue-100 text-blue-800',
    1003: 'bg-yellow-100 text-yellow-800',
    1004: 'bg-red-100 text-red-800',
    1005: 'bg-gray-100 text-gray-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize data
onMounted(() => {
  fetchCheckOffReports()
})
</script>
