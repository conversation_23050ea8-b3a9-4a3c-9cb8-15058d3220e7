<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Loan Limits</h1>
          <p class="text-gray-600 mt-1">Configure and manage loan limits for customers</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
          <button
            @click="refreshData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowPathIcon class="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Filter Cards -->
    <FilterCards
      :filters="filters"
      filter-type="limits"
      :status-options="statusOptions"
      @update:filters="updateFilters"
      @apply="applyFilters"
      @clear="clearFilters"
    />

    <!-- Loan Limits Data Table -->
    <DataTable
      :data="loanLimits"
      :headers="tableHeaders"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Loan Limits"
      row-key="reference_id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="exportData"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowDownTrayIcon class="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </template>

      <!-- Custom columns -->
      <template #cell-merchant_info="{ row }">
        <div class="text-sm">
          <div class="font-medium text-gray-900">{{ row.merchant_name }}</div>
          <div class="text-gray-500">Emp: {{ row.employee_number }}</div>
        </div>
      </template>

      <template #cell-current_limit="{ row }">
        <div class="text-sm font-medium text-gray-900">
          {{ formatCurrency(row.current_limit) }}
        </div>
      </template>

      <template #cell-requested_limit="{ row }">
        <div class="text-sm font-medium text-blue-600">
          {{ formatCurrency(row.requested_limit) }}
        </div>
      </template>

      <template #cell-status="{ row }">
        <span :class="getStatusClass(row.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <template #cell-created_at="{ row }">
        <div class="text-sm text-gray-900">
          {{ formatDate(row.created_at) }}
        </div>
      </template>

      <!-- Actions dropdown -->
      <template #actions="{ row, index }">
        <div class="relative">
          <button
            @click="toggleDropdown(index)"
            class="inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>

          <div
            v-if="showDropdown[index]"
            class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
          >
            <div class="py-1">
              <button
                @click="viewDetails(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                View Details
              </button>
              <button
                v-if="row.status === 2"
                @click="approveLimit(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Approve
              </button>
              <button
                v-if="row.status === 2"
                @click="rejectLimit(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Reject
              </button>
              <button
                v-if="row.status === 2"
                @click="approveLimit(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"
              >
                Approve
              </button>
              <button
                v-if="row.status === 2"
                @click="rejectLimit(row)"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"
              >
                Reject
              </button>
            </div>
          </div>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  ArrowDownTrayIcon,
  EllipsisVerticalIcon
} from '@heroicons/vue/24/outline'
import DataTable from '@/components/DataTable.vue'
import FilterCards from '@/components/FilterCards.vue'
import { loanApi, type LoanLimit } from '@/services/loanApi'
import { useAuthStore } from '@/stores/auth'
import { useBackofficeActions } from '@/composables/useBackofficeActions'

// Router and auth store
const router = useRouter()
const authStore = useAuthStore()

// Use backoffice actions composable
const {
  showDropdown,
  loading,
  currentPage,
  pageSize,
  totalRecords,
  searchQuery,
  toggleDropdown,
  closeDropdown,
  goToPage,
  editRow,
  viewRelatedData,
  handleSearch: handleSearchAction
} = useBackofficeActions()

// Reactive data
const loanLimits = ref<LoanLimit[]>([])
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')

// Filters
const filters = ref({
  client_id: authStore.selectedClientId || '',
  status: '',
  reference_id: '',
  start_date: '',
  end_date: ''
})

// Status options for filters
const statusOptions = [
  { value: '1', label: 'Approved' },
  { value: '2', label: 'Pending' },
  { value: '3', label: 'Rejected' }
]

// Table headers
const tableHeaders = [
  { key: 'reference_id', label: 'Reference ID', sortable: true },
  { key: 'merchant_info', label: 'Organization', sortable: false },
  { key: 'current_limit', label: 'Current Limit', sortable: true },
  { key: 'requested_limit', label: 'Requested Limit', sortable: true },
  { key: 'status', label: 'Status', sortable: true },
  { key: 'created_at', label: 'Date', sortable: true }
]

// Methods
const fetchLoanLimits = async () => {
  loading.value = true
  try {
    const response = await loanApi.getLoanLimits({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      ...filters.value
    })

    if (response.status === 200) {
      loanLimits.value = response.message?.data || []
      totalRecords.value = response.message?.total_count || 0
    } else {
      loanLimits.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error fetching loan limits:', error)
    loanLimits.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

// Filter methods
const updateFilters = (newFilters: Record<string, any>) => {
  Object.assign(filters.value, newFilters)
}

const handlePageChange = (page: number) => {
  goToPage(page, fetchLoanLimits)
}

const handleSearch = (query: string) => {
  handleSearchAction(query, fetchLoanLimits)
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  fetchLoanLimits()
}

const handleRowClick = (row: LoanLimit) => {
  viewDetails(row)
}

const refreshData = () => {
  fetchLoanLimits()
}

const applyFilters = (newFilters: Record<string, any>) => {
  filters.value = { ...newFilters }
  currentPage.value = 1
  fetchLoanLimits()
}

const clearFilters = () => {
  filters.value = {
    client_id: authStore.selectedClientId || '',
    status: '',
    reference_id: '',
    start_date: '',
    end_date: ''
  }
  currentPage.value = 1
  fetchLoanLimits()
}



const viewDetails = (limit: LoanLimit) => {
  // Navigate to limit details page
  router.push({ name: 'loan-limit-details', params: { id: limit.reference_id } })
}

const approveLimit = async (limit: LoanLimit) => {
  try {
    const confirmed = confirm(`Are you sure you want to approve limit request ${limit.reference_id} for ${formatCurrency(limit.requested_limit)}?`)
    if (!confirmed) return

    loading.value = true
    const response = await loanApi.approveLoanLimit(
      limit.reference_id,
      1, // Status 1 = Approved
      `Limit of ${limit.reference_id} approved`
    )

    if (response.status === 200) {
      alert('Loan limit approved successfully')
      fetchLoanLimits() // Refresh the list
    } else {
      alert(`Failed to approve loan limit: ${response.message}`)
    }
  } catch (error) {
    console.error('Error approving loan limit:', error)
    alert('Failed to approve loan limit. Please try again.')
  } finally {
    loading.value = false
  }
}

const rejectLimit = async (limit: LoanLimit) => {
  try {
    const confirmed = confirm(`Are you sure you want to reject limit request ${limit.reference_id}?`)
    if (!confirmed) return

    loading.value = true
    const response = await loanApi.approveLoanLimit(
      limit.reference_id,
      3, // Status 3 = Rejected
      `Limit request ${limit.reference_id} rejected`
    )

    if (response.status === 200) {
      alert('Loan limit rejected successfully')
      fetchLoanLimits() // Refresh the list
    } else {
      alert(`Failed to reject loan limit: ${response.message}`)
    }
  } catch (error) {
    console.error('Error rejecting loan limit:', error)
    alert('Failed to reject loan limit. Please try again.')
  } finally {
    loading.value = false
  }
}

const exportData = () => {
  // Export loan limits data to CSV format
  // Implementation pending API endpoint
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(num)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'Approved',
    2: 'Pending',
    3: 'Rejected'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1: 'bg-green-100 text-green-800',
    2: 'bg-yellow-100 text-yellow-800',
    3: 'bg-red-100 text-red-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize data
onMounted(() => {
  fetchLoanLimits()
})
</script>
