<template>
  <div class="space-y-6">
    <!-- <PERSON>er -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Partners</h1>
          <p class="mt-1 text-sm text-gray-600">
            Manage business partners and their integrations
          </p>
        </div>
        <div class="flex space-x-3">
          <button
            @click="refreshData"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            <svg v-else class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            {{ loading ? 'Refreshing...' : 'Refresh' }}
          </button>
          <button
            @click="exportData"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select
            id="status-filter"
            v-model="filters.status"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
        <div>
          <label for="type-filter" class="block text-sm font-medium text-gray-700 mb-2">Partner Type</label>
          <select
            id="type-filter"
            v-model="filters.partner_type"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Types</option>
            <option value="betting">Betting</option>
            <option value="payment">Payment</option>
            <option value="technology">Technology</option>
            <option value="marketing">Marketing</option>
          </select>
        </div>
        <div>
          <label for="region-filter" class="block text-sm font-medium text-gray-700 mb-2">Region</label>
          <select
            id="region-filter"
            v-model="filters.region"
            @change="applyFilters"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
          >
            <option value="">All Regions</option>
            <option value="africa">Africa</option>
            <option value="europe">Europe</option>
            <option value="asia">Asia</option>
            <option value="americas">Americas</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Partners Table -->
    <DataTable
      :data="partners"
      :loading="loading"
      :current-page="currentPage"
      :total-records="totalRecords"
      :page-size="pageSize"
      title="Business Partners"
      row-key="id"
      :has-actions="true"
      @page-change="handlePageChange"
      @search="handleSearch"
      @sort="handleSort"
      @row-click="handleRowClick"
    >
      <!-- Header Actions Slot -->
      <template #header-actions>
        <div class="flex space-x-2">
          <button
            @click="showAddModal = true"
            class="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add Partner
          </button>
        </div>
      </template>

      <!-- Custom Status Cell -->
      <template #cell-status="{ value }">
        <span
          :class="{
            'bg-green-100 text-green-800': value === 'active',
            'bg-red-100 text-red-800': value === 'inactive',
            'bg-yellow-100 text-yellow-800': value === 'pending',
            'bg-orange-100 text-orange-800': value === 'suspended'
          }"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ value ? value.charAt(0).toUpperCase() + value.slice(1) : '-' }}
        </span>
      </template>

      <!-- Custom Partner Type Cell -->
      <template #cell-partner_type="{ value }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {{ value ? value.charAt(0).toUpperCase() + value.slice(1) : '-' }}
        </span>
      </template>

      <!-- Custom Revenue Cell -->
      <template #cell-monthly_revenue="{ value }">
        <span class="text-gray-900 font-medium">
          {{ value ? `$${value.toLocaleString()}` : '-' }}
        </span>
      </template>

      <!-- Actions Column -->
      <template #actions="{ item }">
        <div class="flex items-center space-x-2">
          <button
            @click="viewPartner(item)"
            class="text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200"
          >
            View
          </button>
          <button
            @click="editPartner(item)"
            class="text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200"
          >
            Edit
          </button>
          <button
            @click="manageServices(item)"
            class="text-green-600 hover:text-green-900 text-sm font-medium transition-colors duration-200"
          >
            Services
          </button>
        </div>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DataTable from '@/components/DataTable.vue'
import { partnerApi } from '@/services/partnerApi'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const partners = ref<any[]>([])
const currentPage = ref(1)
const totalRecords = ref(0)
const pageSize = ref(10)
const searchQuery = ref('')
const sortField = ref('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const showAddModal = ref(false)

// Filters
const filters = reactive({
  status: '',
  partner_type: '',
  region: ''
})

// Methods
const loadData = async () => {
  loading.value = true
  try {
    const response = await partnerApi.getPartners({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: filters.status,
      start: '',
      end: ''
    })

    if (response.status === 200) {
      partners.value = response.message.data || []
      totalRecords.value = response.message.total || 0
    } else {
      console.error('Failed to load partners:', response)
      partners.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Error loading partners:', error)
    partners.value = []
    totalRecords.value = 0
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
  loadData()
}

const handleSort = (field: string, direction: 'asc' | 'desc') => {
  sortField.value = field
  sortDirection.value = direction
  loadData()
}

const handleRowClick = (item: any) => {
  viewPartner(item)
}

const applyFilters = () => {
  currentPage.value = 1
  loadData()
}

const viewPartner = (partner: any) => {
  // Navigate to partner details view
  router.push({ name: 'partner-details', params: { id: partner.id } })
}

const editPartner = (partner: any) => {
  // Navigate to partner edit form
  router.push({ name: 'partner-edit', params: { id: partner.id } })
}

const manageServices = (partner: any) => {
  router.push({ name: 'partner-services', query: { partner_id: partner.id } })
}

const exportData = () => {
  // Export partners data to CSV or Excel format
  // Implementation pending API endpoint
}

// Lifecycle
onMounted(() => {
  loadData()
})
</script>
