<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Organization Configuration</h1>
        <p class="mt-1 text-sm text-gray-500">
          Configure organization settings and limits
        </p>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="successMessage" class="p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
      {{ successMessage }}
    </div>

    <!-- Error Message -->
    <div v-if="errorMessage" class="p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
      {{ errorMessage }}
    </div>

    <!-- Organization Selection -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Select Organization</h3>
      </div>

      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700">Organization</label>
            <select
              v-model="selectedOrganization"
              @change="loadOrganizationConfig"
              class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select an organization</option>
              <option
                v-for="org in organizations"
                :key="org.client_id"
                :value="org"
              >
                {{ org.client_name }} ({{ org.client_account }})
              </option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Forms -->
    <div v-if="selectedOrganization" class="space-y-6">
      <!-- Loan Limits Configuration -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Loan Limits Configuration</h3>
        </div>

        <form @submit.prevent="updateLoanLimits" class="p-6 space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700">Maximum Client Loan Amount</label>
              <input
                v-model.number="loanConfig.max_client_loan"
                type="number"
                min="0"
                step="0.01"
                class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter maximum loan amount"
              />
            </div>

            <div class="flex items-center">
              <input
                id="can_issue_loans"
                v-model="loanConfig.can_issue_loans"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="can_issue_loans" class="ml-2 block text-sm text-gray-900">
                Can Issue Loans
              </label>
            </div>
          </div>

          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ loading ? 'Updating...' : 'Update Loan Limits' }}
            </button>
          </div>
        </form>
      </div>

      <!-- Service Fee Configuration -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Service Fee Configuration</h3>
        </div>

        <form @submit.prevent="updateServiceFee" class="p-6 space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700">Service Fee (%)</label>
              <input
                v-model.number="feeConfig.service_fee"
                type="number"
                min="0"
                max="100"
                step="0.001"
                class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter service fee percentage"
              />
              <p class="mt-1 text-sm text-gray-500">Enter as percentage (e.g., 3.5 for 3.5%)</p>
            </div>
          </div>

          <div class="flex justify-end">
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ loading ? 'Updating...' : 'Update Service Fee' }}
            </button>
          </div>
        </form>
      </div>

      <!-- Organization Status -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Organization Status</h3>
        </div>

        <div class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Current Status</h4>
              <p class="text-sm text-gray-500">
                {{ selectedOrganization.client_status === 1 ? 'Active' : 'Inactive' }}
              </p>
            </div>

            <button
              @click="toggleOrganizationStatus"
              :disabled="loading"
              :class="[
                'px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50',
                selectedOrganization.client_status === 1
                  ? 'text-red-700 bg-red-100 hover:bg-red-200 focus:ring-red-500'
                  : 'text-green-700 bg-green-100 hover:bg-green-200 focus:ring-green-500'
              ]"
            >
              {{ selectedOrganization.client_status === 1 ? 'Deactivate' : 'Activate' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { organizationsApi } from '@/services/organizationsApi'
import type { Organization } from '@/services/types'

// Reactive data
const loading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const organizations = ref<Organization[]>([])
const selectedOrganization = ref<Organization | null>(null)

// Configuration forms
const loanConfig = reactive({
  max_client_loan: 0,
  can_issue_loans: false
})

const feeConfig = reactive({
  service_fee: 0
})

// Load organizations
const loadOrganizations = async () => {
  try {
    const response = await organizationsApi.getOrganizations({ limit: 100 })
    if (response.status === 200) {
      organizations.value = response.message.data || []
    }
  } catch (error) {
    console.error('Error loading organizations:', error)
  }
}

// Load organization configuration
const loadOrganizationConfig = () => {
  if (selectedOrganization.value) {
    loanConfig.max_client_loan = selectedOrganization.value.total_loan_assets || 0
    loanConfig.can_issue_loans = selectedOrganization.value.can_issue_loans === '1'
    feeConfig.service_fee = selectedOrganization.value.service_fee || 0
  }
}

// Update loan limits
const updateLoanLimits = async () => {
  if (!selectedOrganization.value) return

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const response = await organizationsApi.setOrganizationLimits({
      client_account: selectedOrganization.value.client_account,
      max_client_loan: loanConfig.max_client_loan,
      can_issue_loans: loanConfig.can_issue_loans
    })

    if (response.status === 200) {
      successMessage.value = 'Loan limits updated successfully!'
      setTimeout(() => { successMessage.value = '' }, 5000)
    } else {
      errorMessage.value = response.message || 'Failed to update loan limits'
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while updating loan limits'
  } finally {
    loading.value = false
  }
}

// Update service fee
const updateServiceFee = async () => {
  if (!selectedOrganization.value) return

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const response = await organizationsApi.setServiceFee({
      client_account: selectedOrganization.value.client_account,
      service_fee: feeConfig.service_fee
    })

    if (response.status === 200) {
      successMessage.value = 'Service fee updated successfully!'
      setTimeout(() => { successMessage.value = '' }, 5000)
    } else {
      errorMessage.value = response.message || 'Failed to update service fee'
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while updating service fee'
  } finally {
    loading.value = false
  }
}

// Toggle organization status
const toggleOrganizationStatus = async () => {
  if (!selectedOrganization.value) return

  const newStatus = selectedOrganization.value.client_status === 1 ? 0 : 1

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const response = await organizationsApi.updateOrganization({
      client_account: selectedOrganization.value.client_account,
      client_status: newStatus
    })

    if (response.status === 200) {
      selectedOrganization.value.client_status = newStatus
      successMessage.value = `Organization ${newStatus === 1 ? 'activated' : 'deactivated'} successfully!`
      setTimeout(() => { successMessage.value = '' }, 5000)
    } else {
      errorMessage.value = response.message || 'Failed to update organization status'
    }
  } catch (error: any) {
    errorMessage.value = error.message || 'An error occurred while updating organization status'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadOrganizations()
})
</script>
