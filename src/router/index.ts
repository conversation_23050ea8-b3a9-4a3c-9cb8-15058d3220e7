import { createRouter, createWebHistory } from 'vue-router'
import DashboardLayout from '@/layouts/DashboardLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Authentication routes
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/Auth/Login.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('@/views/Auth/ForgotPassword.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/reset-password',
      name: 'reset-password',
      component: () => import('@/views/Auth/ResetPassword.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/verify-otp',
      name: 'verify-otp',
      component: () => import('@/views/Auth/VerifyOTP.vue'),
      meta: { requiresGuest: true }
    },
    // Dashboard routes
    {
      path: '/',
      component: DashboardLayout,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'dashboard',
          component: () => import('@/views/Dashboard.vue')
        },

        {
          path: '/system/roles-permissions',
          name: 'roles-permissions',
          component: () => import('@/views/System/RolesPermissions.vue'),
          meta: { requiresAuth: true }
        },

        // Merchants routes
        {
          path: '/merchants',
          name: 'merchants',
          component: () => import('@/views/Merchants/MerchantsList.vue')
        },
        {
          path: '/merchants/config',
          name: 'merchants-config',
          component: () => import('@/views/Merchants/MerchantsConfig.vue')
        },
        {
          path: '/merchants/bulk',
          name: 'merchants-bulk',
          component: () => import('@/views/Merchants/MerchantsBulkSMS.vue')
        },

        // Financial Operations routes
        {
          path: '/transactions',
          name: 'transactions',
          component: () => import('@/views/Financial/Transactions.vue')
        },


        // Partners routes
        {
          path: '/partners',
          name: 'partners',
          component: () => import('@/views/Partners/Partners.vue')
        },
        {
          path: '/partners/services',
          name: 'partner-services',
          component: () => import('@/views/Partners/PartnerServices.vue')
        },
        {
          path: '/partners/bets',
          name: 'partners-bets',
          component: () => import('@/views/Partners/PartnersBets.vue')
        },
        {
          path: '/partners/bet-slips',
          name: 'partners-bet-slips',
          component: () => import('@/views/Partners/PartnersBetSlips.vue')
        },
        // Services routes
        {
          path: '/services',
          name: 'services',
          component: () => import('@/views/Services/Services.vue')
        },
        // System Administration routes
        {
          path: '/system/users',
          name: 'system-users',
          component: () => import('@/views/System/SystemUsers.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/users/add',
          name: 'add-user',
          component: () => import('@/views/System/AddUser.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/users/edit/:id',
          name: 'edit-user',
          component: () => import('@/views/System/EditUser.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/roles',
          name: 'system-roles',
          component: () => import('@/views/System/SystemRoles.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/roles/add',
          name: 'add-role',
          component: () => import('@/views/System/AddRole.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/roles/edit/:id',
          name: 'edit-role',
          component: () => import('@/views/System/EditRole.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/permissions',
          name: 'system-permissions',
          component: () => import('@/views/System/SystemPermissions.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/permissions/add',
          name: 'add-permission',
          component: () => import('@/views/System/AddPermission.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/permissions/edit/:id',
          name: 'edit-permission',
          component: () => import('@/views/System/EditPermission.vue')
        },
        // Debug route (development only)
        {
          path: '/debug',
          name: 'debug',
          component: () => import('@/views/Debug.vue'),
          meta: { requiresAuth: false }
        }
      ]
    }
  ]
})

// Navigation guards
router.beforeEach(async (to, _from, next) => {
  // Import auth store dynamically to avoid circular dependency
  const { useAuthStore } = await import('@/stores/auth')
  const authStore = useAuthStore()

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login if authentication is required but user is not authenticated
    next({ name: 'login' })
  } else if (requiresGuest && authStore.isAuthenticated) {
    // Redirect to dashboard if guest route but user is authenticated
    next({ name: 'dashboard' })
  } else {
    // Proceed to route
    next()
  }
})

export default router
export { router }
