import { apiClient } from './apiClient'
import { createHashKey } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

// Customer Search interfaces
export interface Customer {
  user_id: string
  loan_number: string
  msisdn: string
  first_name: string
  last_name: string
  national_id: string
  identifier_type: string
  email_address: string
  max_approved_loan_amount: number
  actual_balance: number
  status: number
  kyc_status: number
  pin_status: number
  black_list_state: number
  created_at: string
  updated_at?: string
}

export interface KYCDocument {
  loan_number: string
  file_info: string
  bucket_url: string
  status: number
  created_at: string
}

export interface CustomerTransaction {
  transaction_id: string
  msisdn: string
  first_name: string
  last_name: string
  reference_name: string
  source: string
  amount: number
  currency_code: string
  identifier_name: string
  reference_type: string
  channel_name: string
  description: string
  created: string
}

export interface CustomerFilters {
  loan_number?: string
  client_phone?: string
  client_email?: string
  start?: string
  end?: string
  status?: string
  kyc_status?: string
}

/**
 * Customer Search API service
 */
export const customerSearchApi = {
  /**
   * Search for loan accounts by phone number
   */
  async searchLoanAccount(
    params: { loan_number: string; limit?: number }
  ): Promise<ApiResponse<PaginatedResponse<Customer>>> {
    try {
      const payload = {
        loan_number: params.loan_number,
        limit: params.limit || 10,
        start: '',
        end: ''
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error searching loan account:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get customer transactions
   */
  async getCustomerTransactions(
    params: PaginationParams & {
      client_phone?: string
      start?: string
      end?: string
      source?: string
      amount?: string
      reference_type_id?: string
    } = {}
  ): Promise<ApiResponse<PaginatedResponse<CustomerTransaction>>> {
    try {
      const payload = {
        offset: params.offset || 1,
        limit: params.limit || 10,
        sort: '',
        export: '',
        start: params.start || '',
        end: params.end || '',
        source: params.source || '',
        client_id: '',
        amount: params.amount || '',
        trxn_id: '',
        loan_number: '',
        client_phone: params.client_phone || '',
        client_email: '',
        channel_name: '',
        reference_id: '',
        reference_type_id: params.reference_type_id || '',
        trxn_reference_id: '',
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/transactions', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching customer transactions:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get KYC documents for a customer
   */
  async getKYCDocuments(loanNumber: string): Promise<ApiResponse<KYCDocument>> {
    try {
      const payload = {
        offset: '',
        limit: '',
        sort: '',
        export: '',
        client_id: '',
        client_name: '',
        client_email: '',
        loan_number: loanNumber,
        client_phone: ''
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/kyc', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from API
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching KYC documents:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {},
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Approve or reject KYC
   */
  async approveOrRejectKYC(
    loanNumber: string,
    status: '1' | '2' // 1 = approve, 2 = reject
  ): Promise<ApiResponse> {
    try {
      const payload = {
        status: status,
        acc: loanNumber
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/approve_kyc', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error processing KYC:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to process KYC',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to process KYC',
        code: '500'
      }
    }
  },

  /**
   * Update loan account status
   */
  async updateLoanAccount(
    params: {
      loan_number: string
      blacklist?: number
      blacklist_reason?: string
      pin_status?: number
    }
  ): Promise<ApiResponse> {
    try {
      const payload = {
        blacklist: params.blacklist,
        blacklist_reason: params.blacklist_reason || '',
        pin_status: params.pin_status,
        loan_number: params.loan_number
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/update_account', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating loan account:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update account',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to update account',
        code: '500'
      }
    }
  }
}
