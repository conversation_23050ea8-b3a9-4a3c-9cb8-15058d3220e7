import { apiClient } from './apiClient'
import { createHash<PERSON><PERSON> } from './authApi'
import type { ApiResponse } from './types'

/**
 * Get current timestamp for API requests
 */
const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000)
}

export interface DashboardStats {
  total_users: number
  active_users: number
  total_partners: number
  active_partners: number
  total_bets: number
  total_revenue: number
  pending_transactions: number
  failed_transactions: number
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string
    borderColor?: string
  }[]
}

export const dashboardApi = {
  /**
   * Get dashboard statistics
   */
  async getStats(params: {
    start?: string
    end?: string
    partner_id?: string
  } = {}): Promise<ApiResponse<DashboardStats>> {
    try {
      const payload = {
        start: params.start || '',
        end: params.end || '',
        partner_id: params.partner_id || '',
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('dashboard/v1/view', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Dashboard stats error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {},
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Get dashboard charts data
   */
  async getCharts(params: {
    chart_type?: 'revenue' | 'users' | 'bets' | 'partners'
    period?: 'daily' | 'weekly' | 'monthly'
    start?: string
    end?: string
    partner_id?: string
  } = {}): Promise<ApiResponse<ChartData>> {
    try {
      const payload = {
        chart_type: params.chart_type || 'revenue',
        period: params.period || 'daily',
        start: params.start || '',
        end: params.end || '',
        partner_id: params.partner_id || '',
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('dashboard/v1/charts', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || { labels: [], datasets: [] },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Dashboard charts error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: { labels: [], datasets: [] },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: { labels: [], datasets: [] },
        code: '500'
      }
    }
  }
}
