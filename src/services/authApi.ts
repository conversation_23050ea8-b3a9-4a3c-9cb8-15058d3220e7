import { apiClient } from './apiClient'
import { createAuthHash, createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, Organization, PaginatedResponse } from './types'

// Types
interface LoginPayload {
  username: string
  password: string
  dial_code: string
}

interface LoginWithCodePayload extends LoginPayload {
  verification_code: string
}

interface ForgotPasswordPayload {
  username: string
  dial_code: string
}

interface User {
  id?: number
  username?: string
  email?: string
  role_id: string
  role_name?: string // Role display name
  permissions?: Permission[]
  token?: string
  mc?: number
  clients?: Client[]
  // New fields from API response
  un?: string // User name (display name)
  cn?: string // Company name
  cid?: string // Company ID
  uid?: string // User ID
  expires?: string // Token expiration
  // OTP related fields
  requires_otp?: boolean
  otp_expires_in?: number
  otp_sent?: boolean
}

interface Permission {
  id: string | number // Can be string from API
  name: string
  description?: string
}

interface Client {
  client_id: string
  client_name: string
  client_account: string
}

interface LoginResponse {
  data: User
  message?: string
}

/**
 * Authentication API service
 */
export const authApi = {
  /**
   * Authenticate user with username and password
   */
  async login(payload: LoginPayload): Promise<ApiResponse<LoginResponse>> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('system/v1/user/login', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: {
          data: responseData.data,
          message: responseData.message
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Login error:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: { data: null, message: data.statusDescription || 'Server error' } as any,
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: { data: null, message: data.data.message || 'Login failed' } as any,
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: { data: null, message: 'Network error or server unavailable' } as any,
        code: '500'
      }
    }
  },

  /**
   * Authenticate user with verification code
   */
  async loginWithCode(payload: LoginWithCodePayload): Promise<ApiResponse<LoginResponse>> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('system/v1/user/login_verify', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code,
        message: {
          data: responseData.data,
          message: responseData.message
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Login with code error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: { data: null, message: data.statusDescription || 'Server error' } as any,
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: { data: null, message: data.data.message || 'Login failed' } as any,
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: { data: null, message: 'Network error or server unavailable' } as any,
        code: '500'
      }
    }
  },

  /**
   * Request password reset for user
   */
  async forgotPassword(payload: ForgotPasswordPayload): Promise<ApiResponse> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('system/v1/user/password_reset', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Forgot password error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 422 || status === 500) {
          return {
            status: status,
            message: data.statusDescription || 'Server error',
            code: status.toString()
          }
        } else if (data.data) {
          return {
            status: data.data.code || status,
            message: data.data.message || 'Password reset failed',
            code: (data.data.code || status).toString()
          }
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Verify reset code
   */
  async verifyResetCode(payload: { username: string; verification_code: string; dial_code: string }): Promise<ApiResponse> {
    try {
      const hash = createAuthHash(payload)
      
      const response = await apiClient.post('system/v1/user/verify_reset_code', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Verify reset code error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Verification failed',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Reset password
   */
  async resetPassword(payload: { 
    username: string
    new_password: string
    otp_code: string
    dial_code: string 
  }): Promise<ApiResponse> {
    try {
      // Encode the new password
      const encodedPayload = {
        ...payload,
        new_password: btoa(payload.new_password)
      }
      
      const hash = createAuthHash(encodedPayload)
      
      const response = await apiClient.post('system/v1/user/password_change', encodedPayload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Reset password error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Password reset failed',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Logout
   */
  async logout(): Promise<ApiResponse> {
    try {
      const response = await apiClient.post('merchant/v1/logout')

      return {
        status: 200,
        message: 'Logged out successfully',
        code: '200'
      }
    } catch (error: any) {
      console.error('Logout error:', error)

      // Even if logout fails on server, we should clear local data
      return {
        status: 200,
        message: 'Logged out locally',
        code: '200'
      }
    }
  },

  /**
   * Get organizations list with pagination (MOVED FROM organizationsApi for debugging)
   * This is the SAME function as in organizationsApi.ts but placed here to compare with login
   */
  async getOrganizationsDebug(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Organization>>> {
    try {
      console.log('🔍 DEBUG: Starting getOrganizationsDebug in authApi.ts')

      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 0,
        page: params.page || 1,
        status: "1",
        ...params
      }

      console.log('🔍 DEBUG: Payload prepared:', payload)

      // Create hash for the request
      const hash = createHashKey(payload)
      console.log('🔍 DEBUG: Hash created:', hash)

      console.log('🔍 DEBUG: About to make API call to merchant/v1/view/companies')

      const response = await apiClient.post('merchant/v1/view/companies', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      console.log('🔍 DEBUG: API response received:', response)

      // Handle response structure from old app
      const responseData = response.data.data
      console.log('🔍 DEBUG: Response data extracted:', responseData)

      const result = {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }

      console.log('🔍 DEBUG: Final result prepared:', result)
      return result

    } catch (error: any) {
      console.error('🔍 DEBUG: Error in getOrganizationsDebug:', error)

      if (error.response) {
        const status = error.response.status
        const data = error.response.data

        console.log('🔍 DEBUG: Error response status:', status)
        console.log('🔍 DEBUG: Error response data:', data)

        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }

      console.log('🔍 DEBUG: Network or other error:', error.message)

      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  }
}
