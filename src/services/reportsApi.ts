import { apiClient } from './apiClient'
import { createHashKey } from './authApi'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

/**
 * Get current timestamp for API requests
 */
const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000)
}

export interface SmsOutbox {
  id: number
  recipient: string
  message: string
  status: string
  sent_at?: string
  delivered_at?: string
  failed_reason?: string
  created_at: string
}

export interface SmsFilter {
  id: number
  filter_name: string
  filter_criteria: string
  status: number
  created_at: string
}

export const reportsApi = {
  /**
   * Get SMS outbox messages
   */
  async getSmsOutbox(params: PaginationParams & {
    recipient?: string
    status?: string
    message_type?: string
  } = {}): Promise<ApiResponse<PaginatedResponse<SmsOutbox>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        recipient: params.recipient || '',
        status: params.status || '',
        message_type: params.message_type || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('reports/v1/outbox', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const messages = responseData.data?.data || []
      const mappedMessages = messages.map((msg: any) => ({
        id: parseInt(msg.message_id || msg.id),
        recipient: msg.recipient || msg.phone_number,
        message: msg.message || msg.message_content,
        status: msg.status,
        sent_at: msg.sent_at,
        delivered_at: msg.delivered_at,
        failed_reason: msg.failed_reason || msg.error_message,
        created_at: msg.created_at || new Date().toISOString()
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: mappedMessages,
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get SMS outbox error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get SMS filters
   */
  async getSmsFilters(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<SmsFilter>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        start: params.start || '',
        end: params.end || '',
        status: params.status || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('reports/v1/sms_filters', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const filters = responseData.data?.data || []
      const mappedFilters = filters.map((filter: any) => ({
        id: parseInt(filter.filter_id || filter.id),
        filter_name: filter.filter_name || filter.name,
        filter_criteria: filter.filter_criteria || filter.criteria,
        status: parseInt(filter.status),
        created_at: filter.created_at || new Date().toISOString()
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: mappedFilters,
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get SMS filters error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create SMS blast with filters
   */
  async createSmsBlast(data: {
    message: string
    recipients?: string[]
    filter_criteria?: string
    schedule_time?: string
  }): Promise<ApiResponse<any>> {
    try {
      const payload = {
        message: data.message,
        recipients: data.recipients || [],
        filter_criteria: data.filter_criteria || '',
        schedule_time: data.schedule_time || '',
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('reports/v1/sms_filters', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.message || 'SMS blast created successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Create SMS blast error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to create SMS blast',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to create SMS blast',
        code: '500'
      }
    }
  }
}
