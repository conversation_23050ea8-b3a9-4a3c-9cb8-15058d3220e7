import { apiClient } from './apiClient'
import { createHashKey } from '@/utils/hash'
import type { ApiResponse, PaginationParams, Organization, PaginatedResponse } from './types'

/**
 * Organizations API service
 */
export const organizationsApi = {
  /**
   * Get organizations list with pagination
   */
  async getOrganizations(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<Organization>>> {
    try {
      const payload = {
        limit: params.limit || 10,
        offset: params.offset || 0,
        page: params.page || 1,
        status: "1",
        ...params
      }
      
      // Create hash for the request
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/companies', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      // Handle response structure from old app
      const responseData = response.data.data
      
      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data || [],
          total_count: responseData.total_count || 0,
          current_page: responseData.current_page || 1,
          per_page: responseData.per_page || payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching organizations:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total_count: 0,
            current_page: 1,
            per_page: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total_count: 0,
          current_page: 1,
          per_page: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Add new organization
   */
  async addOrganization(organizationData: Partial<Organization>): Promise<ApiResponse> {
    try {
      const payload = {
        company_name: organizationData.client_name,
        company_phone: organizationData.client_phone,
        company_email: organizationData.client_email,
        company_address: organizationData.client_address,
        contact_person: organizationData.client_name,
        dial_code: "254"
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/create_account', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error adding organization:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to add organization',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Update organization status
   */
  async updateOrganization(organizationData: Partial<Organization>): Promise<ApiResponse> {
    try {
      const payload = {
        client_account: organizationData.client_account,
        status: organizationData.client_status
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/activate', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error updating organization:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to update organization',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Set organization loan limits
   */
  async setOrganizationLimits(data: {
    client_account: string
    max_client_loan: number
    can_issue_loans: boolean
  }): Promise<ApiResponse> {
    try {
      const payload = {
        client_account: data.client_account,
        max_client_loan: data.max_client_loan,
        can_issue_loans: data.can_issue_loans
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/set_limits', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error setting organization limits:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to set organization limits',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Set organization service fee
   */
  async setServiceFee(data: {
    client_account: string
    service_fee: number
  }): Promise<ApiResponse> {
    try {
      const payload = {
        client_account: data.client_account,
        service_fee: data.service_fee
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/set/service_fee', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.message || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error setting service fee:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to set service fee',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  },

  /**
   * Get organization details
   */
  async getOrganizationDetails(clientAccount: string): Promise<ApiResponse<Organization>> {
    try {
      const payload = {
        client_account: clientAccount
      }
      
      const hash = createHashKey(payload)
      
      const response = await apiClient.post('merchant/v1/view/company_details', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })
      
      const responseData = response.data.data
      
      return {
        status: responseData.code,
        message: responseData.data || responseData,
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Error fetching organization details:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to fetch organization details',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Network error or server unavailable',
        code: '500'
      }
    }
  }
}
