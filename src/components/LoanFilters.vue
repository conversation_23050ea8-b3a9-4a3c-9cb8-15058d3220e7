<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 space-y-4">
    <!-- Client Selection -->
    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">Client</label>
      <div class="relative">
        <select
          v-model="localFilters.client_id"
          @change="handleFilterChange"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <option value="">All Clients</option>
          <option 
            v-for="client in clients" 
            :key="client.client_id" 
            :value="client.client_id"
          >
            {{ client.client_name }}
          </option>
        </select>
      </div>
    </div>

    <!-- Collapsible Filter Sections -->
    <div class="space-y-3">
      <!-- Status Filter -->
      <div class="border border-gray-200 rounded-lg">
        <button
          @click="toggleSection('status')"
          class="w-full px-4 py-3 text-left flex items-center justify-between bg-gray-50 hover:bg-gray-100 rounded-t-lg transition-colors duration-200"
        >
          <span class="font-medium text-gray-700">Status Filter</span>
          <ChevronDownIcon 
            :class="['w-5 h-5 text-gray-500 transition-transform duration-200', { 'rotate-180': expandedSections.status }]"
          />
        </button>
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-96"
          leave-active-class="transition-all duration-300 ease-in"
          leave-from-class="opacity-100 max-h-96"
          leave-to-class="opacity-0 max-h-0"
        >
          <div v-if="expandedSections.status" class="px-4 py-3 border-t border-gray-200 overflow-hidden">
            <select
              v-model="localFilters.status"
              @change="handleFilterChange"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">All Statuses</option>
              <option v-for="status in statusOptions" :key="status.value" :value="status.value">
                {{ status.label }}
              </option>
            </select>
          </div>
        </Transition>
      </div>

      <!-- Account/Number Filter -->
      <div class="border border-gray-200 rounded-lg">
        <button
          @click="toggleSection('account')"
          class="w-full px-4 py-3 text-left flex items-center justify-between bg-gray-50 hover:bg-gray-100 rounded-t-lg transition-colors duration-200"
        >
          <span class="font-medium text-gray-700">{{ accountFilterLabel }}</span>
          <ChevronDownIcon 
            :class="['w-5 h-5 text-gray-500 transition-transform duration-200', { 'rotate-180': expandedSections.account }]"
          />
        </button>
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-96"
          leave-active-class="transition-all duration-300 ease-in"
          leave-from-class="opacity-100 max-h-96"
          leave-to-class="opacity-0 max-h-0"
        >
          <div v-if="expandedSections.account" class="px-4 py-3 border-t border-gray-200 overflow-hidden space-y-3">
            <div v-if="showLoanNumber">
              <label class="block text-sm font-medium text-gray-700 mb-1">Loan Number</label>
              <input
                v-model="localFilters.loan_number"
                @input="handleFilterChange"
                type="text"
                placeholder="Enter loan number"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <div v-if="showReferenceId">
              <label class="block text-sm font-medium text-gray-700 mb-1">Reference ID</label>
              <input
                v-model="localFilters.reference_id"
                @input="handleFilterChange"
                type="text"
                placeholder="Enter reference ID"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <div v-if="showRequestNumber">
              <label class="block text-sm font-medium text-gray-700 mb-1">Request Number</label>
              <input
                v-model="localFilters.request_number"
                @input="handleFilterChange"
                type="text"
                placeholder="Enter request number"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </Transition>
      </div>

      <!-- Contact Filter -->
      <div class="border border-gray-200 rounded-lg">
        <button
          @click="toggleSection('contact')"
          class="w-full px-4 py-3 text-left flex items-center justify-between bg-gray-50 hover:bg-gray-100 rounded-t-lg transition-colors duration-200"
        >
          <span class="font-medium text-gray-700">Contact Filter</span>
          <ChevronDownIcon 
            :class="['w-5 h-5 text-gray-500 transition-transform duration-200', { 'rotate-180': expandedSections.contact }]"
          />
        </button>
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-96"
          leave-active-class="transition-all duration-300 ease-in"
          leave-from-class="opacity-100 max-h-96"
          leave-to-class="opacity-0 max-h-0"
        >
          <div v-if="expandedSections.contact" class="px-4 py-3 border-t border-gray-200 overflow-hidden space-y-3">
            <div v-if="showPhoneNumber">
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
              <input
                v-model="localFilters.phone_number"
                @input="handleFilterChange"
                type="text"
                placeholder="Enter phone number"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <div v-if="showNationalId">
              <label class="block text-sm font-medium text-gray-700 mb-1">National ID</label>
              <input
                v-model="localFilters.national_id"
                @input="handleFilterChange"
                type="text"
                placeholder="Enter national ID"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </Transition>
      </div>

      <!-- Date Range Filter -->
      <div class="border border-gray-200 rounded-lg">
        <button
          @click="toggleSection('dateRange')"
          class="w-full px-4 py-3 text-left flex items-center justify-between bg-gray-50 hover:bg-gray-100 rounded-t-lg transition-colors duration-200"
        >
          <span class="font-medium text-gray-700">Date Range</span>
          <ChevronDownIcon 
            :class="['w-5 h-5 text-gray-500 transition-transform duration-200', { 'rotate-180': expandedSections.dateRange }]"
          />
        </button>
        <Transition
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-96"
          leave-active-class="transition-all duration-300 ease-in"
          leave-from-class="opacity-100 max-h-96"
          leave-to-class="opacity-0 max-h-0"
        >
          <div v-if="expandedSections.dateRange" class="px-4 py-3 border-t border-gray-200 overflow-hidden space-y-3">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input
                  v-model="localFilters.start_date"
                  @change="handleFilterChange"
                  type="date"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input
                  v-model="localFilters.end_date"
                  @change="handleFilterChange"
                  type="date"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>

    <!-- Filter Actions -->
    <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
      <button
        @click="applyFilters"
        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
      >
        Apply Filters
      </button>
      <button
        @click="clearFilters"
        class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
      >
        Clear All
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  filters?: Record<string, any>
  filterType?: 'requests' | 'limits' | 'accounts' | 'repayments' | 'products'
  statusOptions?: Array<{ value: string; label: string }>
}

const props = withDefaults(defineProps<Props>(), {
  filters: () => ({}),
  filterType: 'requests',
  statusOptions: () => [
    { value: '1', label: 'Active' },
    { value: '0', label: 'Inactive' },
    { value: '2', label: 'Pending' },
    { value: '3', label: 'Rejected' }
  ]
})

// Emits
const emit = defineEmits<{
  'update:filters': [filters: Record<string, any>]
  'apply': [filters: Record<string, any>]
  'clear': []
}>()

// Auth store
const authStore = useAuthStore()

// Local state
const localFilters = reactive({ ...props.filters })
const expandedSections = reactive({
  status: false,
  account: false,
  contact: false,
  dateRange: false
})

// Computed properties
const clients = computed(() => authStore.clientList || [])

const accountFilterLabel = computed(() => {
  switch (props.filterType) {
    case 'requests':
      return 'Request Details'
    case 'limits':
      return 'Limit Details'
    case 'accounts':
      return 'Account Details'
    case 'repayments':
      return 'Repayment Details'
    default:
      return 'Details'
  }
})

const showLoanNumber = computed(() => 
  ['requests', 'accounts', 'repayments'].includes(props.filterType)
)

const showReferenceId = computed(() => 
  ['requests', 'limits'].includes(props.filterType)
)

const showRequestNumber = computed(() => 
  props.filterType === 'repayments'
)

const showPhoneNumber = computed(() => 
  ['requests', 'accounts'].includes(props.filterType)
)

const showNationalId = computed(() => 
  props.filterType === 'accounts'
)

// Methods
const toggleSection = (section: keyof typeof expandedSections) => {
  expandedSections[section] = !expandedSections[section]
}

const handleFilterChange = () => {
  emit('update:filters', { ...localFilters })
}

const applyFilters = () => {
  emit('apply', { ...localFilters })
}

const clearFilters = () => {
  Object.keys(localFilters).forEach(key => {
    localFilters[key] = ''
  })
  
  // Set default client_id if available
  const defaultClientId = authStore.selectedClientId
  if (defaultClientId) {
    localFilters.client_id = defaultClientId
  }
  
  emit('clear')
  emit('update:filters', { ...localFilters })
}

// Watch for prop changes
watch(() => props.filters, (newFilters) => {
  Object.assign(localFilters, newFilters)
}, { deep: true })

// Initialize with default client
onMounted(() => {
  const defaultClientId = authStore.selectedClientId
  if (defaultClientId && !localFilters.client_id) {
    localFilters.client_id = defaultClientId
    handleFilterChange()
  }
})
</script>
