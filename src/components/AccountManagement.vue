<template>
  <div class="space-y-6">
    <!-- Account Management Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium text-gray-900">Account Management</h3>
        <p class="text-sm text-gray-500">Manage customer account status and settings</p>
      </div>
    </div>

    <!-- Account Information -->
    <div class="bg-gray-50 rounded-lg p-6">
      <h4 class="text-md font-medium text-gray-900 mb-4">Account Information</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700">Customer Name</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.first_name }} {{ customer.last_name }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Phone Number</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.msisdn }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Loan Number</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.loan_number }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Email Address</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.email_address || 'N/A' }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">National ID</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.national_id }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Account Status</label>
          <span :class="getAccountStatusClass(customer.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
            {{ getAccountStatusText(customer.status) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Account Balances -->
    <div class="bg-white border border-gray-200 rounded-lg p-6">
      <h4 class="text-md font-medium text-gray-900 mb-4">Account Balances</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CurrencyDollarIcon class="h-8 w-8 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-blue-900">Max Approved Limit</p>
              <p class="text-2xl font-bold text-blue-600">{{ formatCurrency(customer.max_approved_loan_amount) }}</p>
            </div>
          </div>
        </div>
        <div class="bg-green-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <BanknotesIcon class="h-8 w-8 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-green-900">Current Balance</p>
              <p class="text-2xl font-bold text-green-600">{{ formatCurrency(customer.actual_balance) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Actions -->
    <div class="bg-white border border-gray-200 rounded-lg p-6">
      <h4 class="text-md font-medium text-gray-900 mb-4">Account Actions</h4>
      
      <!-- Blacklist Management -->
      <div class="space-y-4">
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h5 class="text-sm font-medium text-gray-900">Blacklist Status</h5>
            <p class="text-sm text-gray-500">
              Current Status: 
              <span :class="customer.black_list_state === 1 ? 'text-red-600 font-medium' : 'text-green-600 font-medium'">
                {{ customer.black_list_state === 1 ? 'Blacklisted' : 'Active' }}
              </span>
            </p>
          </div>
          <div class="flex space-x-2">
            <button
              v-if="customer.black_list_state !== 1"
              @click="showBlacklistModal = true"
              :disabled="loading"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <ExclamationTriangleIcon class="w-4 h-4 mr-2" />
              Blacklist
            </button>
            <button
              v-else
              @click="unblacklistAccount"
              :disabled="loading"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <CheckIcon class="w-4 h-4 mr-2" />
              Unblacklist
            </button>
          </div>
        </div>

        <!-- PIN Status Management -->
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h5 class="text-sm font-medium text-gray-900">PIN Status</h5>
            <p class="text-sm text-gray-500">
              Current Status: 
              <span :class="customer.pin_status === 1 ? 'text-green-600 font-medium' : 'text-red-600 font-medium'">
                {{ customer.pin_status === 1 ? 'Active' : 'Blocked' }}
              </span>
            </p>
          </div>
          <div class="flex space-x-2">
            <button
              v-if="customer.pin_status !== 1"
              @click="resetPIN"
              :disabled="loading"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <KeyIcon class="w-4 h-4 mr-2" />
              Reset PIN
            </button>
            <button
              v-else
              @click="blockPIN"
              :disabled="loading"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <LockClosedIcon class="w-4 h-4 mr-2" />
              Block PIN
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Blacklist Modal -->
    <div v-if="showBlacklistModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Blacklist Account</h3>
            <button
              @click="showBlacklistModal = false"
              class="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon class="w-6 h-6" />
            </button>
          </div>
          
          <div class="mb-4">
            <label for="blacklist-reason" class="block text-sm font-medium text-gray-700 mb-2">
              Reason for Blacklisting
            </label>
            <select
              id="blacklist-reason"
              v-model="blacklistReason"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a reason</option>
              <option value="1">Fraud</option>
              <option value="2">Self Request</option>
              <option value="3">Non-payment</option>
              <option value="4">Suspicious Activity</option>
            </select>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="showBlacklistModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              @click="blacklistAccount"
              :disabled="!blacklistReason || loading"
              class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              Blacklist Account
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import {
  CurrencyDollarIcon,
  BanknotesIcon,
  ExclamationTriangleIcon,
  CheckIcon,
  KeyIcon,
  LockClosedIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import { customerSearchApi, type Customer } from '@/services/customerSearchApi'

// Props and emits
const props = defineProps<{
  customer: Customer
}>()

const emit = defineEmits<{
  accountUpdated: []
}>()

// Reactive data
const loading = ref(false)
const showBlacklistModal = ref(false)
const blacklistReason = ref('')

// Methods
const blacklistAccount = async () => {
  if (!blacklistReason.value) return

  try {
    const confirmed = confirm(`Are you sure you want to blacklist ${props.customer.first_name} ${props.customer.last_name}?`)
    if (!confirmed) return

    loading.value = true
    const response = await customerSearchApi.updateLoanAccount({
      loan_number: props.customer.loan_number,
      blacklist: 1,
      blacklist_reason: blacklistReason.value
    })

    if (response.status === 200) {
      alert('Account blacklisted successfully')
      showBlacklistModal.value = false
      blacklistReason.value = ''
      emit('accountUpdated')
    } else {
      alert(`Failed to blacklist account: ${response.message}`)
    }
  } catch (error) {
    console.error('Error blacklisting account:', error)
    alert('Failed to blacklist account. Please try again.')
  } finally {
    loading.value = false
  }
}

const unblacklistAccount = async () => {
  try {
    const confirmed = confirm(`Are you sure you want to remove ${props.customer.first_name} ${props.customer.last_name} from blacklist?`)
    if (!confirmed) return

    loading.value = true
    const response = await customerSearchApi.updateLoanAccount({
      loan_number: props.customer.loan_number,
      blacklist: 0,
      blacklist_reason: ''
    })

    if (response.status === 200) {
      alert('Account removed from blacklist successfully')
      emit('accountUpdated')
    } else {
      alert(`Failed to remove from blacklist: ${response.message}`)
    }
  } catch (error) {
    console.error('Error removing from blacklist:', error)
    alert('Failed to remove from blacklist. Please try again.')
  } finally {
    loading.value = false
  }
}

const resetPIN = async () => {
  try {
    const confirmed = confirm(`Are you sure you want to reset PIN for ${props.customer.first_name} ${props.customer.last_name}?`)
    if (!confirmed) return

    loading.value = true
    const response = await customerSearchApi.updateLoanAccount({
      loan_number: props.customer.loan_number,
      pin_status: 1
    })

    if (response.status === 200) {
      alert('PIN reset successfully')
      emit('accountUpdated')
    } else {
      alert(`Failed to reset PIN: ${response.message}`)
    }
  } catch (error) {
    console.error('Error resetting PIN:', error)
    alert('Failed to reset PIN. Please try again.')
  } finally {
    loading.value = false
  }
}

const blockPIN = async () => {
  try {
    const confirmed = confirm(`Are you sure you want to block PIN for ${props.customer.first_name} ${props.customer.last_name}?`)
    if (!confirmed) return

    loading.value = true
    const response = await customerSearchApi.updateLoanAccount({
      loan_number: props.customer.loan_number,
      pin_status: 0
    })

    if (response.status === 200) {
      alert('PIN blocked successfully')
      emit('accountUpdated')
    } else {
      alert(`Failed to block PIN: ${response.message}`)
    }
  } catch (error) {
    console.error('Error blocking PIN:', error)
    alert('Failed to block PIN. Please try again.')
  } finally {
    loading.value = false
  }
}

// Utility functions
const formatCurrency = (amount: number | string) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(num)
}

const getAccountStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1000: 'Active',
    1002: 'New',
    1003: 'Unverified',
    1004: 'Suspended',
    1005: 'Dormant'
  }
  return statusMap[status] || 'Unknown'
}

const getAccountStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    1000: 'bg-green-100 text-green-800',
    1002: 'bg-blue-100 text-blue-800',
    1003: 'bg-yellow-100 text-yellow-800',
    1004: 'bg-red-100 text-red-800',
    1005: 'bg-gray-100 text-gray-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}
</script>
