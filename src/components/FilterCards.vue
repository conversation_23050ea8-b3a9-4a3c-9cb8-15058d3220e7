<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <!-- Status Filter Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-700">Status</h3>
        <button
          @click="toggleSection('status')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ChevronDownIcon 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': expandedSections.status }"
          />
        </button>
      </div>
      
      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-40"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 max-h-40"
        leave-to-class="opacity-0 max-h-0"
      >
        <div v-if="expandedSections.status" class="overflow-hidden">
          <select
            v-model="localFilters.status"
            @change="handleFilterChange"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Status</option>
            <option 
              v-for="option in statusOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
        </div>
      </Transition>
    </div>

    <!-- Client Filter Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-700">Client</h3>
        <button
          @click="toggleSection('client')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ChevronDownIcon 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': expandedSections.client }"
          />
        </button>
      </div>
      
      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-40"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 max-h-40"
        leave-to-class="opacity-0 max-h-0"
      >
        <div v-if="expandedSections.client" class="overflow-hidden">
          <select
            v-model="localFilters.client_id"
            @change="handleFilterChange"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Clients</option>
            <option 
              v-for="client in clients" 
              :key="client.client_id" 
              :value="client.client_id"
            >
              {{ client.client_name }}
            </option>
          </select>
        </div>
      </Transition>
    </div>

    <!-- Account Details Filter Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-700">{{ accountFilterLabel }}</h3>
        <button
          @click="toggleSection('account')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ChevronDownIcon 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': expandedSections.account }"
          />
        </button>
      </div>
      
      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-40"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 max-h-40"
        leave-to-class="opacity-0 max-h-0"
      >
        <div v-if="expandedSections.account" class="overflow-hidden space-y-2">
          <input
            v-if="showLoanNumber"
            v-model="localFilters.loan_number"
            @input="handleFilterChange"
            type="text"
            placeholder="Loan Number"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <input
            v-if="showReferenceId"
            v-model="localFilters.reference_id"
            @input="handleFilterChange"
            type="text"
            placeholder="Reference ID"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <input
            v-if="showRequestNumber"
            v-model="localFilters.request_number"
            @input="handleFilterChange"
            type="text"
            placeholder="Request Number"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </Transition>
    </div>

    <!-- Contact Details Filter Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-gray-700">Contact Details</h3>
        <button
          @click="toggleSection('contact')"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ChevronDownIcon 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': expandedSections.contact }"
          />
        </button>
      </div>
      
      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-40"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 max-h-40"
        leave-to-class="opacity-0 max-h-0"
      >
        <div v-if="expandedSections.contact" class="overflow-hidden space-y-2">
          <input
            v-if="showPhoneNumber"
            v-model="localFilters.phone_number"
            @input="handleFilterChange"
            type="text"
            placeholder="Phone Number"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <input
            v-if="showNationalId"
            v-model="localFilters.national_id"
            @input="handleFilterChange"
            type="text"
            placeholder="National ID"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <input
            v-model="localFilters.customer_name"
            @input="handleFilterChange"
            type="text"
            placeholder="Customer Name"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </Transition>
    </div>

    <!-- CheckOff Filters -->
    <div v-if="filterType === 'checkoff'" class="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div class="p-4">
        <button
          @click="toggleSection('checkoff')"
          class="flex items-center justify-between w-full text-left"
        >
          <h3 class="text-sm font-medium text-gray-900">CheckOff Filters</h3>
          <ChevronDownIcon
            :class="['w-5 h-5 text-gray-500 transition-transform duration-200', { 'rotate-180': expandedSections.checkoff }]"
          />
        </button>
      </div>

      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-40"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 max-h-40"
        leave-to-class="opacity-0 max-h-0"
      >
        <div v-if="expandedSections.checkoff" class="overflow-hidden space-y-2 px-4 pb-4">
          <select
            v-model="localFilters.type_id"
            @change="handleFilterChange"
            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Types</option>
            <option value="1">Type 1</option>
            <option value="2">Type 2</option>
          </select>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <input
              v-model="localFilters.start_date"
              @input="handleFilterChange"
              type="date"
              placeholder="Start Date"
              class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              v-model="localFilters.end_date"
              @input="handleFilterChange"
              type="date"
              placeholder="End Date"
              class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </Transition>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="flex flex-wrap gap-3 mb-6">
    <button
      @click="applyFilters"
      class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
    >
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"/>
      </svg>
      Apply Filters
    </button>
    
    <button
      @click="clearFilters"
      class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
    >
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
      </svg>
      Clear Filters
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  filters?: Record<string, any>
  filterType?: 'requests' | 'limits' | 'accounts' | 'repayments' | 'products' | 'merchants' | 'checkoff'
  statusOptions?: Array<{ value: string; label: string }>
}

const props = withDefaults(defineProps<Props>(), {
  filters: () => ({}),
  filterType: 'requests',
  statusOptions: () => [
    { value: '1', label: 'Active' },
    { value: '0', label: 'Inactive' },
    { value: '2', label: 'Pending' },
    { value: '3', label: 'Rejected' }
  ]
})

// Emits
const emit = defineEmits<{
  'update:filters': [filters: Record<string, any>]
  'apply': [filters: Record<string, any>]
  'clear': []
}>()

// Auth store
const authStore = useAuthStore()

// Local state
const localFilters = reactive({ ...props.filters })
const expandedSections = reactive({
  status: false,
  client: false,
  account: false,
  contact: false,
  checkoff: false
})

// Computed properties
const clients = computed(() => authStore.clientList || [])

const accountFilterLabel = computed(() => {
  switch (props.filterType) {
    case 'requests':
      return 'Request Details'
    case 'limits':
      return 'Limit Details'
    case 'accounts':
      return 'Account Details'
    case 'repayments':
      return 'Repayment Details'
    case 'merchants':
      return 'Merchant Details'
    case 'checkoff':
      return 'CheckOff Details'
    default:
      return 'Details'
  }
})

const showLoanNumber = computed(() => 
  ['requests', 'accounts', 'repayments'].includes(props.filterType)
)

const showReferenceId = computed(() => 
  ['requests', 'limits'].includes(props.filterType)
)

const showRequestNumber = computed(() => 
  props.filterType === 'repayments'
)

const showPhoneNumber = computed(() => 
  ['requests', 'accounts', 'merchants'].includes(props.filterType)
)

const showNationalId = computed(() => 
  props.filterType === 'accounts'
)

// Methods
const toggleSection = (section: keyof typeof expandedSections) => {
  expandedSections[section] = !expandedSections[section]
}

const handleFilterChange = () => {
  emit('update:filters', { ...localFilters })
}

const applyFilters = () => {
  emit('apply', { ...localFilters })
}

const clearFilters = () => {
  Object.keys(localFilters).forEach(key => {
    localFilters[key] = ''
  })
  emit('clear')
  emit('update:filters', { ...localFilters })
}

// Watch for prop changes
watch(() => props.filters, (newFilters) => {
  Object.assign(localFilters, newFilters)
}, { deep: true })

// Auto-expand first section on mount
onMounted(() => {
  expandedSections.status = true
})
</script>
