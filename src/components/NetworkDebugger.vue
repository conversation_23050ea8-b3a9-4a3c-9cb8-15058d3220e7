<template>
  <div class="bg-gray-100 p-4 rounded-lg border">
    <h3 class="text-lg font-semibold mb-4">Network Debugger</h3>
    
    <!-- API Status -->
    <div class="mb-4">
      <h4 class="font-medium mb-2">API Status</h4>
      <div class="flex items-center space-x-2">
        <div :class="apiStatus.color" class="w-3 h-3 rounded-full"></div>
        <span>{{ apiStatus.text }}</span>
        <span class="text-sm text-gray-500">({{ apiBaseUrl }})</span>
      </div>
    </div>

    <!-- Test Buttons -->
    <div class="mb-4">
      <h4 class="font-medium mb-2">Test API Calls</h4>
      <div class="space-x-2">
        <button
          @click="testPartners"
          :disabled="loading"
          class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test Partners
        </button>
        <button
          @click="testAuth"
          :disabled="loading"
          class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test Auth
        </button>
        <button
          @click="testMerchants"
          :disabled="loading"
          class="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test Merchants
        </button>
        <button
          @click="clearLogs"
          class="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Clear Logs
        </button>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div v-if="loading" class="mb-4">
      <div class="flex items-center space-x-2">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        <span>Making API call...</span>
      </div>
    </div>

    <!-- Results -->
    <div class="mb-4">
      <h4 class="font-medium mb-2">Last Response</h4>
      <div class="bg-white p-3 rounded border max-h-40 overflow-y-auto">
        <pre class="text-xs">{{ lastResponse || 'No response yet' }}</pre>
      </div>
    </div>

    <!-- Environment Info -->
    <div class="mb-4">
      <h4 class="font-medium mb-2">Environment Info</h4>
      <div class="text-sm space-y-1">
        <div>Mode: {{ isDev ? 'Development' : 'Production' }}</div>
        <div>Base API: {{ apiBaseUrl }}</div>
        <div>Token: {{ hasToken ? 'Present' : 'Missing' }}</div>
        <div>Partner ID: {{ hasPartnerId ? 'Present' : 'Missing' }}</div>
      </div>
    </div>

    <!-- Console Instructions -->
    <div class="text-sm text-gray-600">
      <p><strong>Instructions:</strong></p>
      <p>1. Open browser DevTools (F12)</p>
      <p>2. Go to Network tab</p>
      <p>3. Click test buttons above</p>
      <p>4. Check Console tab for detailed logs</p>
      <p>5. Look for requests to {{ apiBaseUrl }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { merchantsApi } from '@/services/merchantsApi'
import { authApi } from '@/services/authApi'
import { partnerApi } from '@/services/partnerApi'
import { getApiBaseUrl } from '@/services/apiClient'

const loading = ref(false)
const lastResponse = ref('')
const apiConnected = ref(false)

const apiBaseUrl = getApiBaseUrl()
const isDev = import.meta.env.DEV
const hasToken = computed(() => !!localStorage.getItem('token'))
const hasPartnerId = computed(() => !!localStorage.getItem('selectedPartnerId'))

const apiStatus = computed(() => {
  if (apiConnected.value) {
    return { color: 'bg-green-500', text: 'Connected' }
  } else {
    return { color: 'bg-red-500', text: 'Disconnected' }
  }
})

const testPartners = async () => {
  loading.value = true
  console.log('Testing Partners API...')

  try {
    const response = await partnerApi.getPartners({ limit: 5 })
    lastResponse.value = JSON.stringify(response, null, 2)
    console.log('Partners API Response:', response)
    apiConnected.value = true
  } catch (error) {
    lastResponse.value = `Error: ${error}`
    console.error('Partners API Error:', error)
    apiConnected.value = false
  } finally {
    loading.value = false
  }
}

const testAuth = async () => {
  loading.value = true
  console.log('🧪 Testing Auth API...')

  try {
    const response = await authApi.forgotPassword({ username: 'test', dial_code: '254' })
    lastResponse.value = JSON.stringify(response, null, 2)
    console.log('✅ Auth API Response:', response)
    apiConnected.value = true
  } catch (error) {
    lastResponse.value = `Error: ${error}`
    console.error('❌ Auth API Error:', error)
    apiConnected.value = false
  } finally {
    loading.value = false
  }
}

const testMerchants = async () => {
  loading.value = true
  console.log('🧪 Testing Merchants API...')
  
  try {
    const response = await merchantsApi.getMerchants({ limit: 5 })
    lastResponse.value = JSON.stringify(response, null, 2)
    console.log('✅ Merchants API Response:', response)
    apiConnected.value = true
  } catch (error) {
    lastResponse.value = `Error: ${error}`
    console.error('❌ Merchants API Error:', error)
    apiConnected.value = false
  } finally {
    loading.value = false
  }
}



const clearLogs = () => {
  console.clear()
  lastResponse.value = ''
  console.log('Console cleared')
}

onMounted(() => {
  console.log('🔧 Network Debugger mounted')
  console.log('Environment:', { isDev, apiBaseUrl, hasToken: hasToken.value, hasClientId: hasClientId.value })
})
</script>
