<template>
  <div class="space-y-6">
    <!-- KYC Status Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium text-gray-900">KYC Management</h3>
        <p class="text-sm text-gray-500">Manage customer KYC documents and verification status</p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="refreshKYC"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ArrowPathIcon class="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>
    </div>

    <!-- Customer KYC Info -->
    <div class="bg-gray-50 rounded-lg p-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">Loan Account</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.loan_number }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Identifier Type</label>
          <p class="mt-1 text-sm text-gray-900">{{ customer.identifier_type }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">KYC Status</label>
          <span :class="getKYCStatusClass(customer.kyc_status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1">
            {{ getKYCStatusText(customer.kyc_status) }}
          </span>
        </div>
      </div>
    </div>

    <!-- KYC Documents -->
    <div v-if="kycDocuments" class="bg-white border border-gray-200 rounded-lg p-6">
      <h4 class="text-md font-medium text-gray-900 mb-4">KYC Documents</h4>
      
      <div v-if="imagesLoaded && imagesFound" class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- ID Front -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">ID Front</label>
          <div class="border border-gray-300 rounded-lg overflow-hidden">
            <img
              :src="idFrontImage"
              alt="ID Front"
              class="w-full h-48 object-cover cursor-pointer hover:opacity-75 transition-opacity"
              @click="openImageModal(idFrontImage, 'ID Front')"
              @error="handleImageError"
            />
          </div>
        </div>

        <!-- ID Back -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">ID Back</label>
          <div class="border border-gray-300 rounded-lg overflow-hidden">
            <img
              :src="idBackImage"
              alt="ID Back"
              class="w-full h-48 object-cover cursor-pointer hover:opacity-75 transition-opacity"
              @click="openImageModal(idBackImage, 'ID Back')"
              @error="handleImageError"
            />
          </div>
        </div>

        <!-- Selfie -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">Selfie</label>
          <div class="border border-gray-300 rounded-lg overflow-hidden">
            <img
              :src="selfieImage"
              alt="Selfie"
              class="w-full h-48 object-cover cursor-pointer hover:opacity-75 transition-opacity"
              @click="openImageModal(selfieImage, 'Selfie')"
              @error="handleImageError"
            />
          </div>
        </div>
      </div>

      <div v-else-if="imagesLoaded && !imagesFound" class="text-center py-8">
        <DocumentIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No KYC documents found</h3>
        <p class="mt-1 text-sm text-gray-500">KYC documents have not been uploaded for this customer.</p>
      </div>

      <div v-else class="text-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-2 text-sm text-gray-500">Loading KYC documents...</p>
      </div>

      <!-- KYC Actions -->
      <div v-if="imagesLoaded && imagesFound" class="mt-6 flex justify-center space-x-4">
        <button
          @click="approveKYC"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
        >
          <CheckIcon class="w-4 h-4 mr-2" />
          Approve KYC
        </button>
        <button
          @click="rejectKYC"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
        >
          <XMarkIcon class="w-4 h-4 mr-2" />
          Reject KYC
        </button>
      </div>
    </div>

    <!-- Image Modal -->
    <div v-if="showImageModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50" @click="closeImageModal">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">{{ modalImageTitle }}</h3>
          <button
            @click="closeImageModal"
            class="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>
        <div class="text-center">
          <img
            :src="modalImageSrc"
            :alt="modalImageTitle"
            class="max-w-full max-h-96 mx-auto rounded-lg"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits } from 'vue'
import {
  ArrowPathIcon,
  CheckIcon,
  XMarkIcon,
  DocumentIcon
} from '@heroicons/vue/24/outline'
import { customerSearchApi, type Customer } from '@/services/customerSearchApi'

// Props and emits
const props = defineProps<{
  customer: Customer
}>()

const emit = defineEmits<{
  kycUpdated: []
}>()

// Reactive data
const loading = ref(false)
const kycDocuments = ref<any>(null)
const imagesLoaded = ref(false)
const imagesFound = ref(false)
const idFrontImage = ref('')
const idBackImage = ref('')
const selfieImage = ref('')

// Image modal
const showImageModal = ref(false)
const modalImageSrc = ref('')
const modalImageTitle = ref('')

// Methods
const fetchKYCDocuments = async () => {
  imagesLoaded.value = false
  imagesFound.value = false

  try {
    const response = await customerSearchApi.getKYCDocuments(props.customer.loan_number)

    if (response.status === 200 && response.message) {
      kycDocuments.value = response.message
      
      const bucketUrl = response.message.bucket_url
      const fileInfo = JSON.parse(response.message.data[0].file_info)
      
      const idFront = fileInfo[0].file_name
      const idBack = fileInfo[1].file_name
      const selfie = fileInfo[2].file_name

      idFrontImage.value = bucketUrl + idFront
      idBackImage.value = bucketUrl + idBack
      selfieImage.value = bucketUrl + selfie

      imagesFound.value = true
    } else {
      // Set placeholder images
      const placeholder = "https://via.placeholder.com/600x400?text=Image+Not+Available"
      idFrontImage.value = placeholder
      idBackImage.value = placeholder
      selfieImage.value = placeholder
      
      imagesFound.value = false
    }
  } catch (error) {
    console.error('Error fetching KYC documents:', error)
    imagesFound.value = false
  } finally {
    imagesLoaded.value = true
  }
}

const refreshKYC = () => {
  fetchKYCDocuments()
}

const approveKYC = async () => {
  try {
    const confirmed = confirm(`Are you sure you want to approve KYC for ${props.customer.first_name} ${props.customer.last_name}?`)
    if (!confirmed) return

    loading.value = true
    const response = await customerSearchApi.approveOrRejectKYC(props.customer.loan_number, '1')

    if (response.status === 200) {
      alert('KYC approved successfully')
      emit('kycUpdated')
    } else {
      alert(`Failed to approve KYC: ${response.message}`)
    }
  } catch (error) {
    console.error('Error approving KYC:', error)
    alert('Failed to approve KYC. Please try again.')
  } finally {
    loading.value = false
  }
}

const rejectKYC = async () => {
  try {
    const confirmed = confirm(`Are you sure you want to reject KYC for ${props.customer.first_name} ${props.customer.last_name}?`)
    if (!confirmed) return

    loading.value = true
    const response = await customerSearchApi.approveOrRejectKYC(props.customer.loan_number, '2')

    if (response.status === 200) {
      alert('KYC rejected successfully')
      emit('kycUpdated')
    } else {
      alert(`Failed to reject KYC: ${response.message}`)
    }
  } catch (error) {
    console.error('Error rejecting KYC:', error)
    alert('Failed to reject KYC. Please try again.')
  } finally {
    loading.value = false
  }
}

const openImageModal = (src: string, title: string) => {
  modalImageSrc.value = src
  modalImageTitle.value = title
  showImageModal.value = true
}

const closeImageModal = () => {
  showImageModal.value = false
  modalImageSrc.value = ''
  modalImageTitle.value = ''
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = "https://via.placeholder.com/600x400?text=Image+Not+Available"
}

// Utility functions
const getKYCStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: 'Pending',
    1: 'Approved',
    2: 'Rejected',
    3: 'Under Review'
  }
  return statusMap[status] || 'Unknown'
}

const getKYCStatusClass = (status: number) => {
  const statusClasses: Record<number, string> = {
    0: 'bg-yellow-100 text-yellow-800',
    1: 'bg-green-100 text-green-800',
    2: 'bg-red-100 text-red-800',
    3: 'bg-blue-100 text-blue-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Initialize
onMounted(() => {
  fetchKYCDocuments()
})
</script>
